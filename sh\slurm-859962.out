2025-07-17 13:24:12,512 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac_disentangled.json
2025-07-17 13:24:12,513 [trainer.py] => prefix: disentangled_reproduce
2025-07-17 13:24:12,514 [trainer.py] => dataset: cub
2025-07-17 13:24:12,514 [trainer.py] => memory_size: 0
2025-07-17 13:24:12,514 [trainer.py] => shuffle: True
2025-07-17 13:24:12,514 [trainer.py] => init_cls: 100
2025-07-17 13:24:12,514 [trainer.py] => increment: 10
2025-07-17 13:24:12,514 [trainer.py] => model_name: ranpac
2025-07-17 13:24:12,514 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-17 13:24:12,515 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-17 13:24:12,515 [trainer.py] => seed: 1993
2025-07-17 13:24:12,515 [trainer.py] => resume: False
2025-07-17 13:24:12,515 [trainer.py] => shot: 5
2025-07-17 13:24:12,515 [trainer.py] => use_simplecil: False
2025-07-17 13:24:12,515 [trainer.py] => tuned_epoch: 40
2025-07-17 13:24:12,515 [trainer.py] => init_lr: 0.01
2025-07-17 13:24:12,515 [trainer.py] => batch_size: 48
2025-07-17 13:24:12,515 [trainer.py] => weight_decay: 0.0005
2025-07-17 13:24:12,516 [trainer.py] => min_lr: 0
2025-07-17 13:24:12,516 [trainer.py] => ffn_num: 64
2025-07-17 13:24:12,516 [trainer.py] => optimizer: sgd
2025-07-17 13:24:12,516 [trainer.py] => use_RP: True
2025-07-17 13:24:12,516 [trainer.py] => M: 10000
2025-07-17 13:24:12,516 [trainer.py] => fecam: False
2025-07-17 13:24:12,516 [trainer.py] => calibration: True
2025-07-17 13:24:12,516 [trainer.py] => knn_k: 5
2025-07-17 13:24:12,516 [trainer.py] => knn_distance_metric: cosine
2025-07-17 13:24:12,516 [trainer.py] => knn_weight_decay: 0.1
2025-07-17 13:24:12,516 [trainer.py] => knn_adaptive_k: True
2025-07-17 13:24:12,516 [trainer.py] => knn_temperature: 16.0
2025-07-17 13:24:12,517 [trainer.py] => k_min: 3
2025-07-17 13:24:12,517 [trainer.py] => k_max: 21
2025-07-17 13:24:12,517 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-17 13:24:12,517 [trainer.py] => cosine_temperature: 16.0
2025-07-17 13:24:12,517 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-17 13:24:12,517 [trainer.py] => use_disentangled_adapter: True
2025-07-17 13:24:12,517 [trainer.py] => identity_bottleneck: 64
2025-07-17 13:24:12,517 [trainer.py] => variation_bottleneck: 64
2025-07-17 13:24:12,517 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-17 13:24:12,517 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-17 13:24:12,517 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-17 13:24:12,517 [trainer.py] => decorrelation_weight: 0.1
2025-07-17 13:24:12,517 [trainer.py] => orthogonal_weight: 0.01
2025-07-17 13:24:12,518 [trainer.py] => mutual_info_weight: 0.05
2025-07-17 13:24:12,518 [trainer.py] => contrastive_weight: 0.03
2025-07-17 13:24:12,518 [trainer.py] => disentangle_temperature: 0.1
2025-07-17 13:24:12,518 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-17 13:24:12,518 [trainer.py] => orthogonal_constraint: True
2025-07-17 13:24:12,518 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-17 13:24:12,518 [trainer.py] => variation_cov_weight: 0.8
2025-07-17 13:24:12,518 [trainer.py] => identity_similarity_weight: 1.0
2025-07-17 13:24:12,518 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-17 13:24:12,518 [trainer.py] => min_samples_for_cov: 5
2025-07-17 13:24:12,518 [trainer.py] => identity_similarity_metric: cosine
2025-07-17 13:24:12,518 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-17 13:24:12,519 [trainer.py] => identity_similarity_top_k: 5
2025-07-17 13:24:12,636 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.identity_branch.0.weight', 'blocks.0.adaptmlp.identity_branch.0.bias', 'blocks.0.adaptmlp.variation_branch.0.weight', 'blocks.0.adaptmlp.variation_branch.0.bias', 'blocks.0.adaptmlp.identity_output_proj.weight', 'blocks.0.adaptmlp.identity_output_proj.bias', 'blocks.0.adaptmlp.variation_output_proj.weight', 'blocks.0.adaptmlp.variation_output_proj.bias', 'blocks.1.adaptmlp.identity_branch.0.weight', 'blocks.1.adaptmlp.identity_branch.0.bias', 'blocks.1.adaptmlp.variation_branch.0.weight', 'blocks.1.adaptmlp.variation_branch.0.bias', 'blocks.1.adaptmlp.identity_output_proj.weight', 'blocks.1.adaptmlp.identity_output_proj.bias', 'blocks.1.adaptmlp.variation_output_proj.weight', 'blocks.1.adaptmlp.variation_output_proj.bias', 'blocks.2.adaptmlp.identity_branch.0.weight', 'blocks.2.adaptmlp.identity_branch.0.bias', 'blocks.2.adaptmlp.variation_branch.0.weight', 'blocks.2.adaptmlp.variation_branch.0.bias', 'blocks.2.adaptmlp.identity_output_proj.weight', 'blocks.2.adaptmlp.identity_output_proj.bias', 'blocks.2.adaptmlp.variation_output_proj.weight', 'blocks.2.adaptmlp.variation_output_proj.bias', 'blocks.3.adaptmlp.identity_branch.0.weight', 'blocks.3.adaptmlp.identity_branch.0.bias', 'blocks.3.adaptmlp.variation_branch.0.weight', 'blocks.3.adaptmlp.variation_branch.0.bias', 'blocks.3.adaptmlp.identity_output_proj.weight', 'blocks.3.adaptmlp.identity_output_proj.bias', 'blocks.3.adaptmlp.variation_output_proj.weight', 'blocks.3.adaptmlp.variation_output_proj.bias', 'blocks.4.adaptmlp.identity_branch.0.weight', 'blocks.4.adaptmlp.identity_branch.0.bias', 'blocks.4.adaptmlp.variation_branch.0.weight', 'blocks.4.adaptmlp.variation_branch.0.bias', 'blocks.4.adaptmlp.identity_output_proj.weight', 'blocks.4.adaptmlp.identity_output_proj.bias', 'blocks.4.adaptmlp.variation_output_proj.weight', 'blocks.4.adaptmlp.variation_output_proj.bias', 'blocks.5.adaptmlp.identity_branch.0.weight', 'blocks.5.adaptmlp.identity_branch.0.bias', 'blocks.5.adaptmlp.variation_branch.0.weight', 'blocks.5.adaptmlp.variation_branch.0.bias', 'blocks.5.adaptmlp.identity_output_proj.weight', 'blocks.5.adaptmlp.identity_output_proj.bias', 'blocks.5.adaptmlp.variation_output_proj.weight', 'blocks.5.adaptmlp.variation_output_proj.bias', 'blocks.6.adaptmlp.identity_branch.0.weight', 'blocks.6.adaptmlp.identity_branch.0.bias', 'blocks.6.adaptmlp.variation_branch.0.weight', 'blocks.6.adaptmlp.variation_branch.0.bias', 'blocks.6.adaptmlp.identity_output_proj.weight', 'blocks.6.adaptmlp.identity_output_proj.bias', 'blocks.6.adaptmlp.variation_output_proj.weight', 'blocks.6.adaptmlp.variation_output_proj.bias', 'blocks.7.adaptmlp.identity_branch.0.weight', 'blocks.7.adaptmlp.identity_branch.0.bias', 'blocks.7.adaptmlp.variation_branch.0.weight', 'blocks.7.adaptmlp.variation_branch.0.bias', 'blocks.7.adaptmlp.identity_output_proj.weight', 'blocks.7.adaptmlp.identity_output_proj.bias', 'blocks.7.adaptmlp.variation_output_proj.weight', 'blocks.7.adaptmlp.variation_output_proj.bias', 'blocks.8.adaptmlp.identity_branch.0.weight', 'blocks.8.adaptmlp.identity_branch.0.bias', 'blocks.8.adaptmlp.variation_branch.0.weight', 'blocks.8.adaptmlp.variation_branch.0.bias', 'blocks.8.adaptmlp.identity_output_proj.weight', 'blocks.8.adaptmlp.identity_output_proj.bias', 'blocks.8.adaptmlp.variation_output_proj.weight', 'blocks.8.adaptmlp.variation_output_proj.bias', 'blocks.9.adaptmlp.identity_branch.0.weight', 'blocks.9.adaptmlp.identity_branch.0.bias', 'blocks.9.adaptmlp.variation_branch.0.weight', 'blocks.9.adaptmlp.variation_branch.0.bias', 'blocks.9.adaptmlp.identity_output_proj.weight', 'blocks.9.adaptmlp.identity_output_proj.bias', 'blocks.9.adaptmlp.variation_output_proj.weight', 'blocks.9.adaptmlp.variation_output_proj.bias', 'blocks.10.adaptmlp.identity_branch.0.weight', 'blocks.10.adaptmlp.identity_branch.0.bias', 'blocks.10.adaptmlp.variation_branch.0.weight', 'blocks.10.adaptmlp.variation_branch.0.bias', 'blocks.10.adaptmlp.identity_output_proj.weight', 'blocks.10.adaptmlp.identity_output_proj.bias', 'blocks.10.adaptmlp.variation_output_proj.weight', 'blocks.10.adaptmlp.variation_output_proj.bias', 'blocks.11.adaptmlp.identity_branch.0.weight', 'blocks.11.adaptmlp.identity_branch.0.bias', 'blocks.11.adaptmlp.variation_branch.0.weight', 'blocks.11.adaptmlp.variation_branch.0.bias', 'blocks.11.adaptmlp.identity_output_proj.weight', 'blocks.11.adaptmlp.identity_output_proj.bias', 'blocks.11.adaptmlp.variation_output_proj.weight', 'blocks.11.adaptmlp.variation_output_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-17 13:24:48,301 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-17 13:24:48,301 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-17 13:24:48,301 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-17 13:24:48,301 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-17 13:24:48,302 [trainer.py] => All params: 87578880
2025-07-17 13:24:48,303 [trainer.py] => Trainable params: 1780224
2025-07-17 13:24:49,543 [ranpac.py] => Learning on 0-100
87,655,681 total parameters.
1,857,025 training parameters.
backbone.blocks.0.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.0.adaptmlp.identity_branch.0.bias 64
backbone.blocks.0.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.0.adaptmlp.variation_branch.0.bias 64
backbone.blocks.0.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.0.adaptmlp.identity_output_proj.bias 384
backbone.blocks.0.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.0.adaptmlp.variation_output_proj.bias 384
backbone.blocks.1.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.1.adaptmlp.identity_branch.0.bias 64
backbone.blocks.1.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.1.adaptmlp.variation_branch.0.bias 64
backbone.blocks.1.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.1.adaptmlp.identity_output_proj.bias 384
backbone.blocks.1.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.1.adaptmlp.variation_output_proj.bias 384
backbone.blocks.2.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.2.adaptmlp.identity_branch.0.bias 64
backbone.blocks.2.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.2.adaptmlp.variation_branch.0.bias 64
backbone.blocks.2.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.2.adaptmlp.identity_output_proj.bias 384
backbone.blocks.2.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.2.adaptmlp.variation_output_proj.bias 384
backbone.blocks.3.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.3.adaptmlp.identity_branch.0.bias 64
backbone.blocks.3.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.3.adaptmlp.variation_branch.0.bias 64
backbone.blocks.3.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.3.adaptmlp.identity_output_proj.bias 384
backbone.blocks.3.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.3.adaptmlp.variation_output_proj.bias 384
backbone.blocks.4.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.4.adaptmlp.identity_branch.0.bias 64
backbone.blocks.4.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.4.adaptmlp.variation_branch.0.bias 64
backbone.blocks.4.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.4.adaptmlp.identity_output_proj.bias 384
backbone.blocks.4.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.4.adaptmlp.variation_output_proj.bias 384
backbone.blocks.5.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.5.adaptmlp.identity_branch.0.bias 64
backbone.blocks.5.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.5.adaptmlp.variation_branch.0.bias 64
backbone.blocks.5.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.5.adaptmlp.identity_output_proj.bias 384
backbone.blocks.5.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.5.adaptmlp.variation_output_proj.bias 384
backbone.blocks.6.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.6.adaptmlp.identity_branch.0.bias 64
backbone.blocks.6.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.6.adaptmlp.variation_branch.0.bias 64
backbone.blocks.6.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.6.adaptmlp.identity_output_proj.bias 384
backbone.blocks.6.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.6.adaptmlp.variation_output_proj.bias 384
backbone.blocks.7.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.7.adaptmlp.identity_branch.0.bias 64
backbone.blocks.7.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.7.adaptmlp.variation_branch.0.bias 64
backbone.blocks.7.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.7.adaptmlp.identity_output_proj.bias 384
backbone.blocks.7.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.7.adaptmlp.variation_output_proj.bias 384
backbone.blocks.8.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.8.adaptmlp.identity_branch.0.bias 64
backbone.blocks.8.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.8.adaptmlp.variation_branch.0.bias 64
backbone.blocks.8.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.8.adaptmlp.identity_output_proj.bias 384
backbone.blocks.8.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.8.adaptmlp.variation_output_proj.bias 384
backbone.blocks.9.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.9.adaptmlp.identity_branch.0.bias 64
backbone.blocks.9.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.9.adaptmlp.variation_branch.0.bias 64
backbone.blocks.9.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.9.adaptmlp.identity_output_proj.bias 384
backbone.blocks.9.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.9.adaptmlp.variation_output_proj.bias 384
backbone.blocks.10.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.10.adaptmlp.identity_branch.0.bias 64
backbone.blocks.10.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.10.adaptmlp.variation_branch.0.bias 64
backbone.blocks.10.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.10.adaptmlp.identity_output_proj.bias 384
backbone.blocks.10.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.10.adaptmlp.variation_output_proj.bias 384
backbone.blocks.11.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.11.adaptmlp.identity_branch.0.bias 64
backbone.blocks.11.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.11.adaptmlp.variation_branch.0.bias 64
backbone.blocks.11.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.11.adaptmlp.identity_output_proj.bias 384
backbone.blocks.11.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.11.adaptmlp.variation_output_proj.bias 384
fc.weight 76800
fc.sigma 1

  0%|          | 0/40 [00:00<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.17, Test_accy 8.56:   0%|          | 0/40 [00:48<?, ?it/s]
Task 0, Epoch 1/40 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.17, Test_accy 8.56:   2%|▎         | 1/40 [00:48<31:21, 48.23s/it]
Task 0, Epoch 2/40 => Loss 4.532, Disentangle_Loss 2.475, Train_accy 19.39, Test_accy 41.35:   2%|▎         | 1/40 [01:32<31:21, 48.23s/it]
Task 0, Epoch 2/40 => Loss 4.532, Disentangle_Loss 2.475, Train_accy 19.39, Test_accy 41.35:   5%|▌         | 2/40 [01:32<29:01, 45.83s/it]
Task 0, Epoch 3/40 => Loss 4.385, Disentangle_Loss 3.751, Train_accy 47.48, Test_accy 65.62:   5%|▌         | 2/40 [02:13<29:01, 45.83s/it]
Task 0, Epoch 3/40 => Loss 4.385, Disentangle_Loss 3.751, Train_accy 47.48, Test_accy 65.62:   8%|▊         | 3/40 [02:13<27:03, 43.89s/it]
Task 0, Epoch 4/40 => Loss 3.985, Disentangle_Loss 5.029, Train_accy 64.80, Test_accy 77.19:   8%|▊         | 3/40 [02:52<27:03, 43.89s/it]
Task 0, Epoch 4/40 => Loss 3.985, Disentangle_Loss 5.029, Train_accy 64.80, Test_accy 77.19:  10%|█         | 4/40 [02:52<25:02, 41.73s/it]
Task 0, Epoch 5/40 => Loss 3.087, Disentangle_Loss 6.310, Train_accy 73.57, Test_accy 83.02:  10%|█         | 4/40 [03:34<25:02, 41.73s/it]
Task 0, Epoch 5/40 => Loss 3.087, Disentangle_Loss 6.310, Train_accy 73.57, Test_accy 83.02:  12%|█▎        | 5/40 [03:34<24:22, 41.80s/it]
Task 0, Epoch 6/40 => Loss 1.926, Disentangle_Loss 7.590, Train_accy 78.45, Test_accy 86.83:  12%|█▎        | 5/40 [04:21<24:22, 41.80s/it]
Task 0, Epoch 6/40 => Loss 1.926, Disentangle_Loss 7.590, Train_accy 78.45, Test_accy 86.83:  15%|█▌        | 6/40 [04:21<24:47, 43.74s/it]
Task 0, Epoch 7/40 => Loss 1.227, Disentangle_Loss 8.870, Train_accy 82.82, Test_accy 88.25:  15%|█▌        | 6/40 [05:03<24:47, 43.74s/it]
Task 0, Epoch 7/40 => Loss 1.227, Disentangle_Loss 8.870, Train_accy 82.82, Test_accy 88.25:  18%|█▊        | 7/40 [05:03<23:45, 43.19s/it]
Task 0, Epoch 8/40 => Loss 0.979, Disentangle_Loss 10.148, Train_accy 83.98, Test_accy 89.67:  18%|█▊        | 7/40 [05:47<23:45, 43.19s/it]
Task 0, Epoch 8/40 => Loss 0.979, Disentangle_Loss 10.148, Train_accy 83.98, Test_accy 89.67:  20%|██        | 8/40 [05:47<23:06, 43.34s/it]
Task 0, Epoch 9/40 => Loss 0.827, Disentangle_Loss 11.428, Train_accy 84.98, Test_accy 89.88:  20%|██        | 8/40 [06:31<23:06, 43.34s/it]
Task 0, Epoch 9/40 => Loss 0.827, Disentangle_Loss 11.428, Train_accy 84.98, Test_accy 89.88:  22%|██▎       | 9/40 [06:31<22:27, 43.48s/it]
Task 0, Epoch 10/40 => Loss 0.752, Disentangle_Loss 12.706, Train_accy 86.02, Test_accy 90.16:  22%|██▎       | 9/40 [07:17<22:27, 43.48s/it]
Task 0, Epoch 10/40 => Loss 0.752, Disentangle_Loss 12.706, Train_accy 86.02, Test_accy 90.16:  25%|██▌       | 10/40 [07:17<22:05, 44.19s/it]
Task 0, Epoch 11/40 => Loss 0.724, Disentangle_Loss 13.984, Train_accy 85.85, Test_accy 90.71:  25%|██▌       | 10/40 [08:04<22:05, 44.19s/it]
Task 0, Epoch 11/40 => Loss 0.724, Disentangle_Loss 13.984, Train_accy 85.85, Test_accy 90.71:  28%|██▊       | 11/40 [08:04<21:48, 45.13s/it]
Task 0, Epoch 12/40 => Loss 0.665, Disentangle_Loss 13.771, Train_accy 87.09, Test_accy 91.06:  28%|██▊       | 11/40 [08:49<21:48, 45.13s/it]
Task 0, Epoch 12/40 => Loss 0.665, Disentangle_Loss 13.771, Train_accy 87.09, Test_accy 91.06:  30%|███       | 12/40 [08:49<21:01, 45.06s/it]
Task 0, Epoch 13/40 => Loss 0.591, Disentangle_Loss 13.559, Train_accy 88.79, Test_accy 91.16:  30%|███       | 12/40 [09:32<21:01, 45.06s/it]
Task 0, Epoch 13/40 => Loss 0.591, Disentangle_Loss 13.559, Train_accy 88.79, Test_accy 91.16:  32%|███▎      | 13/40 [09:32<19:59, 44.41s/it]
Task 0, Epoch 14/40 => Loss 0.559, Disentangle_Loss 13.346, Train_accy 89.19, Test_accy 91.47:  32%|███▎      | 13/40 [10:18<19:59, 44.41s/it]
Task 0, Epoch 14/40 => Loss 0.559, Disentangle_Loss 13.346, Train_accy 89.19, Test_accy 91.47:  35%|███▌      | 14/40 [10:18<19:30, 45.01s/it]
Task 0, Epoch 15/40 => Loss 0.569, Disentangle_Loss 13.133, Train_accy 88.59, Test_accy 91.40:  35%|███▌      | 14/40 [11:05<19:30, 45.01s/it]
Task 0, Epoch 15/40 => Loss 0.569, Disentangle_Loss 13.133, Train_accy 88.59, Test_accy 91.40:  38%|███▊      | 15/40 [11:05<18:58, 45.54s/it]
Task 0, Epoch 16/40 => Loss 0.542, Disentangle_Loss 12.920, Train_accy 89.52, Test_accy 91.47:  38%|███▊      | 15/40 [11:52<18:58, 45.54s/it]
Task 0, Epoch 16/40 => Loss 0.542, Disentangle_Loss 12.920, Train_accy 89.52, Test_accy 91.47:  40%|████      | 16/40 [11:52<18:24, 46.03s/it]
Task 0, Epoch 17/40 => Loss 0.530, Disentangle_Loss 12.707, Train_accy 89.39, Test_accy 92.10:  40%|████      | 16/40 [12:41<18:24, 46.03s/it]
Task 0, Epoch 17/40 => Loss 0.530, Disentangle_Loss 12.707, Train_accy 89.39, Test_accy 92.10:  42%|████▎     | 17/40 [12:41<17:59, 46.92s/it]
Task 0, Epoch 18/40 => Loss 0.526, Disentangle_Loss 12.494, Train_accy 89.99, Test_accy 91.99:  42%|████▎     | 17/40 [13:28<17:59, 46.92s/it]
Task 0, Epoch 18/40 => Loss 0.526, Disentangle_Loss 12.494, Train_accy 89.99, Test_accy 91.99:  45%|████▌     | 18/40 [13:28<17:09, 46.82s/it]
Task 0, Epoch 19/40 => Loss 0.489, Disentangle_Loss 12.281, Train_accy 90.09, Test_accy 91.96:  45%|████▌     | 18/40 [14:14<17:09, 46.82s/it]
Task 0, Epoch 19/40 => Loss 0.489, Disentangle_Loss 12.281, Train_accy 90.09, Test_accy 91.96:  48%|████▊     | 19/40 [14:14<16:20, 46.71s/it]
Task 0, Epoch 20/40 => Loss 0.511, Disentangle_Loss 12.068, Train_accy 89.56, Test_accy 92.20:  48%|████▊     | 19/40 [15:06<16:20, 46.71s/it]
Task 0, Epoch 20/40 => Loss 0.511, Disentangle_Loss 12.068, Train_accy 89.56, Test_accy 92.20:  50%|█████     | 20/40 [15:06<16:06, 48.32s/it]
Task 0, Epoch 21/40 => Loss 0.485, Disentangle_Loss 11.855, Train_accy 90.62, Test_accy 92.20:  50%|█████     | 20/40 [15:56<16:06, 48.32s/it]
Task 0, Epoch 21/40 => Loss 0.485, Disentangle_Loss 11.855, Train_accy 90.62, Test_accy 92.20:  52%|█████▎    | 21/40 [15:56<15:25, 48.74s/it]
Task 0, Epoch 22/40 => Loss 0.474, Disentangle_Loss 11.642, Train_accy 90.76, Test_accy 92.03:  52%|█████▎    | 21/40 [16:42<15:25, 48.74s/it]
Task 0, Epoch 22/40 => Loss 0.474, Disentangle_Loss 11.642, Train_accy 90.76, Test_accy 92.03:  55%|█████▌    | 22/40 [16:42<14:24, 48.05s/it]
Task 0, Epoch 23/40 => Loss 0.476, Disentangle_Loss 11.428, Train_accy 90.39, Test_accy 92.41:  55%|█████▌    | 22/40 [17:31<14:24, 48.05s/it]
Task 0, Epoch 23/40 => Loss 0.476, Disentangle_Loss 11.428, Train_accy 90.39, Test_accy 92.41:  57%|█████▊    | 23/40 [17:31<13:40, 48.29s/it]
Task 0, Epoch 24/40 => Loss 0.446, Disentangle_Loss 11.215, Train_accy 91.39, Test_accy 92.03:  57%|█████▊    | 23/40 [18:14<13:40, 48.29s/it]
Task 0, Epoch 24/40 => Loss 0.446, Disentangle_Loss 11.215, Train_accy 91.39, Test_accy 92.03:  60%|██████    | 24/40 [18:14<12:28, 46.80s/it]
Task 0, Epoch 25/40 => Loss 0.450, Disentangle_Loss 11.002, Train_accy 91.12, Test_accy 92.24:  60%|██████    | 24/40 [18:55<12:28, 46.80s/it]
Task 0, Epoch 25/40 => Loss 0.450, Disentangle_Loss 11.002, Train_accy 91.12, Test_accy 92.24:  62%|██████▎   | 25/40 [18:55<11:15, 45.06s/it]
Task 0, Epoch 26/40 => Loss 0.467, Disentangle_Loss 10.789, Train_accy 90.42, Test_accy 92.27:  62%|██████▎   | 25/40 [19:39<11:15, 45.06s/it]
Task 0, Epoch 26/40 => Loss 0.467, Disentangle_Loss 10.789, Train_accy 90.42, Test_accy 92.27:  65%|██████▌   | 26/40 [19:39<10:23, 44.54s/it]
Task 0, Epoch 27/40 => Loss 0.459, Disentangle_Loss 10.576, Train_accy 90.59, Test_accy 92.41:  65%|██████▌   | 26/40 [20:19<10:23, 44.54s/it]
Task 0, Epoch 27/40 => Loss 0.459, Disentangle_Loss 10.576, Train_accy 90.59, Test_accy 92.41:  68%|██████▊   | 27/40 [20:19<09:20, 43.12s/it]
Task 0, Epoch 28/40 => Loss 0.410, Disentangle_Loss 10.364, Train_accy 92.63, Test_accy 92.48:  68%|██████▊   | 27/40 [21:04<09:20, 43.12s/it]
Task 0, Epoch 28/40 => Loss 0.410, Disentangle_Loss 10.364, Train_accy 92.63, Test_accy 92.48:  70%|███████   | 28/40 [21:04<08:47, 43.95s/it]
Task 0, Epoch 29/40 => Loss 0.428, Disentangle_Loss 10.150, Train_accy 91.69, Test_accy 92.34:  70%|███████   | 28/40 [21:54<08:47, 43.95s/it]
Task 0, Epoch 29/40 => Loss 0.428, Disentangle_Loss 10.150, Train_accy 91.69, Test_accy 92.34:  72%|███████▎  | 29/40 [21:54<08:22, 45.68s/it]
Task 0, Epoch 30/40 => Loss 0.437, Disentangle_Loss 9.937, Train_accy 91.73, Test_accy 92.48:  72%|███████▎  | 29/40 [22:36<08:22, 45.68s/it] 
Task 0, Epoch 30/40 => Loss 0.437, Disentangle_Loss 9.937, Train_accy 91.73, Test_accy 92.48:  75%|███████▌  | 30/40 [22:36<07:24, 44.49s/it]
Task 0, Epoch 31/40 => Loss 0.432, Disentangle_Loss 9.724, Train_accy 91.76, Test_accy 92.44:  75%|███████▌  | 30/40 [23:20<07:24, 44.49s/it]
Task 0, Epoch 31/40 => Loss 0.432, Disentangle_Loss 9.724, Train_accy 91.76, Test_accy 92.44:  78%|███████▊  | 31/40 [23:20<06:40, 44.53s/it]
Task 0, Epoch 32/40 => Loss 0.440, Disentangle_Loss 9.511, Train_accy 90.82, Test_accy 92.55:  78%|███████▊  | 31/40 [24:09<06:40, 44.53s/it]
Task 0, Epoch 32/40 => Loss 0.440, Disentangle_Loss 9.511, Train_accy 90.82, Test_accy 92.55:  80%|████████  | 32/40 [24:09<06:06, 45.76s/it]
Task 0, Epoch 33/40 => Loss 0.416, Disentangle_Loss 9.298, Train_accy 91.79, Test_accy 92.48:  80%|████████  | 32/40 [24:53<06:06, 45.76s/it]
Task 0, Epoch 33/40 => Loss 0.416, Disentangle_Loss 9.298, Train_accy 91.79, Test_accy 92.48:  82%|████████▎ | 33/40 [24:53<05:16, 45.24s/it]
Task 0, Epoch 34/40 => Loss 0.419, Disentangle_Loss 9.085, Train_accy 91.79, Test_accy 92.44:  82%|████████▎ | 33/40 [25:43<05:16, 45.24s/it]
Task 0, Epoch 34/40 => Loss 0.419, Disentangle_Loss 9.085, Train_accy 91.79, Test_accy 92.44:  85%|████████▌ | 34/40 [25:43<04:39, 46.55s/it]
Task 0, Epoch 35/40 => Loss 0.425, Disentangle_Loss 8.871, Train_accy 91.93, Test_accy 92.48:  85%|████████▌ | 34/40 [26:30<04:39, 46.55s/it]
Task 0, Epoch 35/40 => Loss 0.425, Disentangle_Loss 8.871, Train_accy 91.93, Test_accy 92.48:  88%|████████▊ | 35/40 [26:30<03:54, 46.83s/it]
Task 0, Epoch 36/40 => Loss 0.394, Disentangle_Loss 8.659, Train_accy 92.86, Test_accy 92.41:  88%|████████▊ | 35/40 [27:17<03:54, 46.83s/it]
Task 0, Epoch 36/40 => Loss 0.394, Disentangle_Loss 8.659, Train_accy 92.86, Test_accy 92.41:  90%|█████████ | 36/40 [27:17<03:07, 46.83s/it]
Task 0, Epoch 37/40 => Loss 0.411, Disentangle_Loss 8.445, Train_accy 92.19, Test_accy 92.41:  90%|█████████ | 36/40 [28:05<03:07, 46.83s/it]
Task 0, Epoch 37/40 => Loss 0.411, Disentangle_Loss 8.445, Train_accy 92.19, Test_accy 92.41:  92%|█████████▎| 37/40 [28:05<02:21, 47.26s/it]
Task 0, Epoch 38/40 => Loss 0.406, Disentangle_Loss 8.233, Train_accy 92.23, Test_accy 92.44:  92%|█████████▎| 37/40 [28:52<02:21, 47.26s/it]
Task 0, Epoch 38/40 => Loss 0.406, Disentangle_Loss 8.233, Train_accy 92.23, Test_accy 92.44:  95%|█████████▌| 38/40 [28:52<01:34, 47.12s/it]
Task 0, Epoch 39/40 => Loss 0.402, Disentangle_Loss 8.019, Train_accy 92.53, Test_accy 92.41:  95%|█████████▌| 38/40 [29:41<01:34, 47.12s/it]
Task 0, Epoch 39/40 => Loss 0.402, Disentangle_Loss 8.019, Train_accy 92.53, Test_accy 92.41:  98%|█████████▊| 39/40 [29:41<00:47, 47.61s/it]
Task 0, Epoch 40/40 => Loss 0.417, Disentangle_Loss 7.806, Train_accy 92.26, Test_accy 92.41:  98%|█████████▊| 39/40 [30:31<00:47, 47.61s/it]
Task 0, Epoch 40/40 => Loss 0.417, Disentangle_Loss 7.806, Train_accy 92.26, Test_accy 92.41: 100%|██████████| 40/40 [30:31<00:00, 48.37s/it]
Task 0, Epoch 40/40 => Loss 0.417, Disentangle_Loss 7.806, Train_accy 92.26, Test_accy 92.41: 100%|██████████| 40/40 [30:31<00:00, 45.79s/it]
2025-07-17 13:55:21,089 [ranpac.py] => Task 0, Epoch 40/40 => Loss 0.417, Disentangle_Loss 7.806, Train_accy 92.26, Test_accy 92.41
2025-07-17 13:55:44,773 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
selected lambda =  1000000.0
2025-07-17 13:56:14,921 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-17 13:56:15,366 [ranpac.py] => [Dynamic-K] Base similarity range: [0.1131, 1.1047]
2025-07-17 13:56:15,366 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-17 13:56:15,367 [trainer.py] => No NME accuracy.
2025-07-17 13:56:15,367 [trainer.py] => CNN: {'total': 92.41, '00-99': 92.41, 'old': 0, 'new': 92.41}
2025-07-17 13:56:15,369 [trainer.py] => CNN HM: [0.0]
2025-07-17 13:56:15,369 [trainer.py] => CNN top1 curve: [92.41]
Average Accuracy (CNN): 92.41
2025-07-17 13:56:15,369 [trainer.py] => Average Accuracy (CNN): 92.41 

2025-07-17 13:56:15,370 [trainer.py] => All params: 88578881
2025-07-17 13:56:15,370 [trainer.py] => Trainable params: 1780225
2025-07-17 13:56:15,381 [ranpac.py] => Learning on 100-110
2025-07-17 13:56:21,213 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:56:21,229 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:56:21,231 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:56:21,232 [ranpac.py] => [Disentangled KNN] task 1, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:56:21,234 [ranpac.py] => [Disentangled KNN] task 1, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:56:21,235 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:56:21,310 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
/home/<USER>/workdir/FSCIL-Calibration-main/utils/disentangled_covariance.py:271: RuntimeWarning: covariance is not positive-semidefinite.
  samples = np.random.multivariate_normal(
2025-07-17 13:56:23,209 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:56:46,580 [trainer.py] => No NME accuracy.
2025-07-17 13:56:46,582 [trainer.py] => CNN: {'total': 91.64, '00-99': 91.92, '100-109': 88.85, 'old': 91.92, 'new': 88.85}
2025-07-17 13:56:46,582 [trainer.py] => CNN HM: [0.0, 90.359]
2025-07-17 13:56:46,582 [trainer.py] => CNN top1 curve: [92.41, 91.64]
Average Accuracy (CNN): 92.025
2025-07-17 13:56:46,583 [trainer.py] => Average Accuracy (CNN): 92.025 

2025-07-17 13:56:46,583 [trainer.py] => All params: 88678881
2025-07-17 13:56:46,584 [trainer.py] => Trainable params: 2880225
2025-07-17 13:56:46,595 [ranpac.py] => Learning on 110-120
2025-07-17 13:56:49,277 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:56:49,293 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:56:49,296 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:56:49,297 [ranpac.py] => [Disentangled KNN] task 2, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:56:49,298 [ranpac.py] => [Disentangled KNN] task 2, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:56:49,300 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:56:49,374 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:56:51,121 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:57:18,490 [trainer.py] => No NME accuracy.
2025-07-17 13:57:18,492 [trainer.py] => CNN: {'total': 90.13, '00-99': 91.79, '100-109': 88.85, '110-119': 74.65, 'old': 91.51, 'new': 74.65}
2025-07-17 13:57:18,492 [trainer.py] => CNN HM: [0.0, 90.359, 82.225]
2025-07-17 13:57:18,492 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13]
Average Accuracy (CNN): 91.39333333333333
2025-07-17 13:57:18,492 [trainer.py] => Average Accuracy (CNN): 91.39333333333333 

2025-07-17 13:57:18,493 [trainer.py] => All params: 88778881
2025-07-17 13:57:18,494 [trainer.py] => Trainable params: 2980225
2025-07-17 13:57:18,506 [ranpac.py] => Learning on 120-130
2025-07-17 13:57:21,676 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:57:21,691 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:57:21,692 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:57:21,693 [ranpac.py] => [Disentangled KNN] task 3, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:57:21,693 [ranpac.py] => [Disentangled KNN] task 3, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:57:21,693 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:57:21,770 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:57:23,541 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:57:51,563 [trainer.py] => No NME accuracy.
2025-07-17 13:57:51,566 [trainer.py] => CNN: {'total': 88.74, '00-99': 91.33, '100-109': 89.86, '110-119': 75.0, '120-129': 75.17, 'old': 89.87, 'new': 75.17}
2025-07-17 13:57:51,566 [trainer.py] => CNN HM: [0.0, 90.359, 82.225, 81.865]
2025-07-17 13:57:51,566 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13, 88.74]
Average Accuracy (CNN): 90.73
2025-07-17 13:57:51,566 [trainer.py] => Average Accuracy (CNN): 90.73 

2025-07-17 13:57:51,567 [trainer.py] => All params: 88878881
2025-07-17 13:57:51,567 [trainer.py] => Trainable params: 3080225
2025-07-17 13:57:51,582 [ranpac.py] => Learning on 130-140
2025-07-17 13:57:56,598 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:57:56,616 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:57:56,618 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:57:56,619 [ranpac.py] => [Disentangled KNN] task 4, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:57:56,621 [ranpac.py] => [Disentangled KNN] task 4, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:57:56,622 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:57:56,695 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:57:58,457 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:58:26,905 [trainer.py] => No NME accuracy.
2025-07-17 13:58:26,907 [trainer.py] => CNN: {'total': 88.31, '00-99': 91.2, '100-109': 89.86, '110-119': 75.7, '120-129': 72.41, '130-139': 86.21, 'old': 88.47, 'new': 86.21}
2025-07-17 13:58:26,907 [trainer.py] => CNN HM: [0.0, 90.359, 82.225, 81.865, 87.325]
2025-07-17 13:58:26,907 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13, 88.74, 88.31]
Average Accuracy (CNN): 90.24600000000001
2025-07-17 13:58:26,907 [trainer.py] => Average Accuracy (CNN): 90.24600000000001 

2025-07-17 13:58:26,908 [trainer.py] => All params: 88978881
2025-07-17 13:58:26,909 [trainer.py] => Trainable params: 3180225
2025-07-17 13:58:26,925 [ranpac.py] => Learning on 140-150
2025-07-17 13:58:31,009 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:58:31,027 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:58:31,029 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:58:31,030 [ranpac.py] => [Disentangled KNN] task 5, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:58:31,031 [ranpac.py] => [Disentangled KNN] task 5, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:58:31,033 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:58:31,106 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:58:32,834 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:59:07,457 [trainer.py] => No NME accuracy.
2025-07-17 13:59:07,459 [trainer.py] => CNN: {'total': 86.84, '00-99': 90.88, '100-109': 89.86, '110-119': 73.24, '120-129': 71.72, '130-139': 85.86, '140-149': 72.3, 'old': 87.84, 'new': 72.3}
2025-07-17 13:59:07,459 [trainer.py] => CNN HM: [0.0, 90.359, 82.225, 81.865, 87.325, 79.316]
2025-07-17 13:59:07,459 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13, 88.74, 88.31, 86.84]
Average Accuracy (CNN): 89.67833333333334
2025-07-17 13:59:07,459 [trainer.py] => Average Accuracy (CNN): 89.67833333333334 

2025-07-17 13:59:07,460 [trainer.py] => All params: 89078881
2025-07-17 13:59:07,461 [trainer.py] => Trainable params: 3280225
2025-07-17 13:59:07,476 [ranpac.py] => Learning on 150-160
2025-07-17 13:59:10,524 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:59:10,538 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:59:10,541 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:59:10,542 [ranpac.py] => [Disentangled KNN] task 6, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:59:10,543 [ranpac.py] => [Disentangled KNN] task 6, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:59:10,544 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:59:10,618 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:59:12,357 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:59:40,016 [trainer.py] => No NME accuracy.
2025-07-17 13:59:40,018 [trainer.py] => CNN: {'total': 86.44, '00-99': 90.78, '100-109': 88.51, '110-119': 73.94, '120-129': 72.76, '130-139': 85.17, '140-149': 72.66, '150-159': 81.51, 'old': 86.77, 'new': 81.51}
2025-07-17 13:59:40,018 [trainer.py] => CNN HM: [0.0, 90.359, 82.225, 81.865, 87.325, 79.316, 84.058]
2025-07-17 13:59:40,018 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13, 88.74, 88.31, 86.84, 86.44]
Average Accuracy (CNN): 89.21571428571428
2025-07-17 13:59:40,018 [trainer.py] => Average Accuracy (CNN): 89.21571428571428 

2025-07-17 13:59:40,019 [trainer.py] => All params: 89178881
2025-07-17 13:59:40,020 [trainer.py] => Trainable params: 3380225
2025-07-17 13:59:40,035 [ranpac.py] => Learning on 160-170
2025-07-17 13:59:44,586 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:59:44,604 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:59:44,607 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:59:44,608 [ranpac.py] => [Disentangled KNN] task 7, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:59:44,609 [ranpac.py] => [Disentangled KNN] task 7, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:59:44,610 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:59:44,687 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:59:46,413 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:00:17,048 [trainer.py] => No NME accuracy.
2025-07-17 14:00:17,050 [trainer.py] => CNN: {'total': 85.77, '00-99': 90.54, '100-109': 89.19, '110-119': 72.89, '120-129': 72.76, '130-139': 84.83, '140-149': 67.63, '150-159': 81.16, '160-169': 83.56, 'old': 85.92, 'new': 83.56}
2025-07-17 14:00:17,050 [trainer.py] => CNN HM: [0.0, 90.359, 82.225, 81.865, 87.325, 79.316, 84.058, 84.724]
2025-07-17 14:00:17,051 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13, 88.74, 88.31, 86.84, 86.44, 85.77]
Average Accuracy (CNN): 88.785
2025-07-17 14:00:17,051 [trainer.py] => Average Accuracy (CNN): 88.785 

2025-07-17 14:00:17,052 [trainer.py] => All params: 89278881
2025-07-17 14:00:17,053 [trainer.py] => Trainable params: 3480225
2025-07-17 14:00:17,071 [ranpac.py] => Learning on 170-180
2025-07-17 14:00:21,131 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:00:21,148 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:00:21,151 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:00:21,152 [ranpac.py] => [Disentangled KNN] task 8, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:00:21,153 [ranpac.py] => [Disentangled KNN] task 8, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:00:21,155 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:00:21,229 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 14:00:22,949 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:00:55,520 [trainer.py] => No NME accuracy.
2025-07-17 14:00:55,522 [trainer.py] => CNN: {'total': 85.53, '00-99': 90.33, '100-109': 89.19, '110-119': 71.48, '120-129': 72.41, '130-139': 85.52, '140-149': 68.71, '150-159': 80.82, '160-169': 83.56, '170-179': 83.89, 'old': 85.63, 'new': 83.89}
2025-07-17 14:00:55,522 [trainer.py] => CNN HM: [0.0, 90.359, 82.225, 81.865, 87.325, 79.316, 84.058, 84.724, 84.751]
2025-07-17 14:00:55,522 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13, 88.74, 88.31, 86.84, 86.44, 85.77, 85.53]
Average Accuracy (CNN): 88.42333333333333
2025-07-17 14:00:55,522 [trainer.py] => Average Accuracy (CNN): 88.42333333333333 

2025-07-17 14:00:55,524 [trainer.py] => All params: 89378881
2025-07-17 14:00:55,525 [trainer.py] => Trainable params: 3580225
2025-07-17 14:00:55,544 [ranpac.py] => Learning on 180-190
2025-07-17 14:00:58,719 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:00:58,734 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:00:58,736 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:00:58,737 [ranpac.py] => [Disentangled KNN] task 9, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:00:58,738 [ranpac.py] => [Disentangled KNN] task 9, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:00:58,739 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:00:58,813 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 14:01:00,552 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:01:45,334 [trainer.py] => No NME accuracy.
2025-07-17 14:01:45,337 [trainer.py] => CNN: {'total': 84.98, '00-99': 90.16, '100-109': 89.86, '110-119': 71.13, '120-129': 72.41, '130-139': 83.45, '140-149': 68.71, '150-159': 81.85, '160-169': 83.22, '170-179': 83.56, '180-189': 78.05, 'old': 85.36, 'new': 78.05}
2025-07-17 14:01:45,337 [trainer.py] => CNN HM: [0.0, 90.359, 82.225, 81.865, 87.325, 79.316, 84.058, 84.724, 84.751, 81.541]
2025-07-17 14:01:45,337 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13, 88.74, 88.31, 86.84, 86.44, 85.77, 85.53, 84.98]
Average Accuracy (CNN): 88.079
2025-07-17 14:01:45,337 [trainer.py] => Average Accuracy (CNN): 88.079 

2025-07-17 14:01:45,339 [trainer.py] => All params: 89478881
2025-07-17 14:01:45,340 [trainer.py] => Trainable params: 3680225
2025-07-17 14:01:45,357 [ranpac.py] => Learning on 190-200
2025-07-17 14:01:48,664 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:01:48,682 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:01:48,684 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:01:48,686 [ranpac.py] => [Disentangled KNN] task 10, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:01:48,688 [ranpac.py] => [Disentangled KNN] task 10, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:01:48,689 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:01:48,763 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 14:01:50,514 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:02:26,066 [trainer.py] => No NME accuracy.
2025-07-17 14:02:26,069 [trainer.py] => CNN: {'total': 84.12, '00-99': 89.91, '100-109': 89.86, '110-119': 71.83, '120-129': 71.03, '130-139': 83.45, '140-149': 68.35, '150-159': 82.19, '160-169': 81.88, '170-179': 82.89, '180-189': 78.05, '190-199': 73.31, 'old': 84.7, 'new': 73.31}
2025-07-17 14:02:26,070 [trainer.py] => CNN HM: [0.0, 90.359, 82.225, 81.865, 87.325, 79.316, 84.058, 84.724, 84.751, 81.541, 78.594]
2025-07-17 14:02:26,070 [trainer.py] => CNN top1 curve: [92.41, 91.64, 90.13, 88.74, 88.31, 86.84, 86.44, 85.77, 85.53, 84.98, 84.12]
Average Accuracy (CNN): 87.71909090909091
2025-07-17 14:02:26,071 [trainer.py] => Average Accuracy (CNN): 87.71909090909091 

Accuracy Matrix (CNN):
[[92.41 91.92 91.79 91.33 91.2  90.88 90.78 90.54 90.33 90.16 89.91]
 [ 0.   88.85 88.85 89.86 89.86 89.86 88.51 89.19 89.19 89.86 89.86]
 [ 0.    0.   74.65 75.   75.7  73.24 73.94 72.89 71.48 71.13 71.83]
 [ 0.    0.    0.   75.17 72.41 71.72 72.76 72.76 72.41 72.41 71.03]
 [ 0.    0.    0.    0.   86.21 85.86 85.17 84.83 85.52 83.45 83.45]
 [ 0.    0.    0.    0.    0.   72.3  72.66 67.63 68.71 68.71 68.35]
 [ 0.    0.    0.    0.    0.    0.   81.51 81.16 80.82 81.85 82.19]
 [ 0.    0.    0.    0.    0.    0.    0.   83.56 83.56 83.22 81.88]
 [ 0.    0.    0.    0.    0.    0.    0.    0.   83.89 83.56 82.89]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.   78.05 78.05]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.    0.   73.31]]
2025-07-17 14:02:26,073 [trainer.py] => Forgetting (CNN): 2.0260000000000007
