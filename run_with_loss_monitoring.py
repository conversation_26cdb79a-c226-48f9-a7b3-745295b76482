#!/usr/bin/env python3
"""
带损失监控的训练启动脚本
自动配置日志输出，方便查看各项损失的详细信息
"""

import os
import sys
import logging
import argparse
from datetime import datetime

def setup_detailed_logging(log_dir="./detailed_logs"):
    """设置详细的日志输出"""
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"training_with_losses_{timestamp}.log")
    
    # 配置日志格式
    log_format = '%(asctime)s [%(filename)s:%(lineno)d] => %(message)s'
    
    # 清除现有的handlers
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 设置新的logging配置
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),  # 控制台输出
            logging.FileHandler(log_file, encoding='utf-8')  # 文件输出
        ]
    )
    
    print(f"✓ 详细日志将保存到: {log_file}")
    return log_file

def print_config_summary(config_path):
    """打印配置文件摘要"""
    try:
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print(f"\n{'='*60}")
        print(f"配置文件摘要: {config_path}")
        print(f"{'='*60}")
        
        # 基础配置
        print(f"数据集: {config.get('dataset', 'N/A')}")
        print(f"模型: {config.get('model_name', 'N/A')}")
        print(f"骨干网络: {config.get('backbone_type', 'N/A')}")
        print(f"训练轮数: {config.get('tuned_epoch', 'N/A')}")
        print(f"批次大小: {config.get('batch_size', 'N/A')}")
        
        # 解耦适配器配置
        if config.get('use_disentangled_adapter', False):
            print(f"\n🔧 解耦适配器配置:")
            print(f"  - 身份瓶颈维度: {config.get('identity_bottleneck', 'N/A')}")
            print(f"  - 变化瓶颈维度: {config.get('variation_bottleneck', 'N/A')}")
            print(f"  - 解耦损失权重: {config.get('disentangle_loss_weight', 'N/A')}")
            print(f"  - 使用身份特征进行KNN: {config.get('use_identity_features_for_knn', 'N/A')}")
            
            print(f"\n📊 解耦损失组件权重:")
            print(f"  - 去相关权重: {config.get('decorrelation_weight', 'N/A')}")
            print(f"  - 正交权重: {config.get('orthogonal_weight', 'N/A')}")
            print(f"  - 互信息权重: {config.get('mutual_info_weight', 'N/A')}")
            print(f"  - 对比权重: {config.get('contrastive_weight', 'N/A')}")
            print(f"  - 解耦温度: {config.get('disentangle_temperature', 'N/A')}")
        else:
            print(f"\n⚠️  解耦适配器未启用")
        
        # 校准配置
        if config.get('calibration', False):
            print(f"\n🎯 校准配置:")
            print(f"  - KNN K值: {config.get('knn_k', 'N/A')}")
            print(f"  - 距离度量: {config.get('knn_distance_metric', 'N/A')}")
            print(f"  - 自适应K: {config.get('knn_adaptive_k', 'N/A')}")
            print(f"  - KNN温度: {config.get('knn_temperature', 'N/A')}")
            
            if config.get('use_disentangled_adapter', False):
                print(f"  - 变化协方差权重: {config.get('variation_cov_weight', 'N/A')}")
                print(f"  - 身份相似度权重: {config.get('identity_similarity_weight', 'N/A')}")
        
        print(f"{'='*60}\n")
        
    except Exception as e:
        print(f"⚠️  无法读取配置文件: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='带损失监控的FSCIL训练')
    parser.add_argument('--config', type=str, default='exps/ranpac_disentangled.json',
                       help='配置文件路径')
    parser.add_argument('--log_dir', type=str, default='./detailed_logs',
                       help='详细日志保存目录')
    parser.add_argument('--test_only', action='store_true',
                       help='仅测试损失打印功能，不进行实际训练')
    
    args = parser.parse_args()
    
    print("🚀 带损失监控的FSCIL训练启动器")
    print("=" * 60)
    
    # 设置详细日志
    log_file = setup_detailed_logging(args.log_dir)
    
    # 打印配置摘要
    print_config_summary(args.config)
    
    if args.test_only:
        print("🧪 运行损失打印测试...")
        try:
            # 运行测试脚本
            import subprocess
            result = subprocess.run([sys.executable, 'test_loss_printing.py'], 
                                  capture_output=True, text=True)
            
            print("测试输出:")
            print(result.stdout)
            
            if result.stderr:
                print("测试错误:")
                print(result.stderr)
            
            if result.returncode == 0:
                print("✅ 损失打印测试通过！")
            else:
                print("❌ 损失打印测试失败！")
                
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    else:
        print("🎯 启动实际训练...")
        print("💡 提示: 训练过程中将显示详细的损失信息")
        print("📝 所有日志将保存到:", log_file)
        print("🔍 关注以下关键信息:")
        print("   - [Disentangled Loss Details]: 每20个batch的详细损失")
        print("   - [Loss Components]: 各项解耦损失的具体数值")
        print("   - [Loss Ratios]: 各项损失的占比")
        print("   - [Epoch Summary]: 每个epoch的损失汇总")
        print("   - Epoch汇总表格: 详细的统计信息")
        print()
        
        try:
            # 导入并运行主训练脚本
            from main import main as train_main
            import sys
            
            # 设置命令行参数
            original_argv = sys.argv
            sys.argv = ['main.py', '--config', args.config]
            
            # 运行训练
            train_main()
            
            # 恢复原始参数
            sys.argv = original_argv
            
            print(f"\n✅ 训练完成！详细日志已保存到: {log_file}")
            
        except KeyboardInterrupt:
            print(f"\n⚠️  训练被用户中断")
        except Exception as e:
            print(f"\n❌ 训练过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🏁 程序结束")

if __name__ == "__main__":
    main()
