"""
解耦损失函数模块
实现各种解耦约束机制，确保身份向量和变化向量之间的互信息最小化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class DisentanglementLossManager(nn.Module):
    """
    解耦损失管理器，集成多种解耦约束机制
    """
    def __init__(self, 
                 decorrelation_weight=1.0,
                 orthogonal_weight=0.1,
                 mutual_info_weight=0.5,
                 contrastive_weight=0.3,
                 temperature=0.1):
        super().__init__()
        
        self.decorrelation_weight = decorrelation_weight
        self.orthogonal_weight = orthogonal_weight
        self.mutual_info_weight = mutual_info_weight
        self.contrastive_weight = contrastive_weight
        self.temperature = temperature
        
    def compute_decorrelation_loss(self, identity_features, variation_features):
        """
        计算去相关损失，最小化身份和变化特征之间的相关性（数值稳定版本）

        Args:
            identity_features: [batch_size, identity_dim] 身份特征
            variation_features: [batch_size, variation_dim] 变化特征

        Returns:
            decorrelation_loss: 去相关损失
        """
        try:
            # 检查输入有效性
            if torch.isnan(identity_features).any() or torch.isnan(variation_features).any():
                return torch.tensor(0.0, device=identity_features.device, requires_grad=True)

            # 中心化特征
            identity_centered = identity_features - identity_features.mean(dim=0, keepdim=True)
            variation_centered = variation_features - variation_features.mean(dim=0, keepdim=True)

            # 添加小的噪声防止零向量
            eps = 1e-8
            identity_centered = identity_centered + eps * torch.randn_like(identity_centered)
            variation_centered = variation_centered + eps * torch.randn_like(variation_centered)

            # 安全的归一化
            identity_norm = identity_centered / (torch.norm(identity_centered, dim=0, keepdim=True) + eps)
            variation_norm = variation_centered / (torch.norm(variation_centered, dim=0, keepdim=True) + eps)

            # 计算互相关矩阵
            cross_correlation = torch.mm(identity_norm.T, variation_norm)

            # 检查结果有效性
            if torch.isnan(cross_correlation).any():
                return torch.tensor(0.0, device=identity_features.device, requires_grad=True)

            # Frobenius范数作为去相关损失
            decorrelation_loss = torch.norm(cross_correlation, p='fro') ** 2

            # 限制损失范围
            decorrelation_loss = torch.clamp(decorrelation_loss, 0, 100.0)

            return decorrelation_loss * self.decorrelation_weight

        except Exception as e:
            # 异常情况下返回零损失
            return torch.tensor(0.0, device=identity_features.device, requires_grad=True)
    
    def compute_orthogonal_loss(self, features):
        """
        计算正交损失，促进特征内部的正交性（数值稳定版本）

        Args:
            features: [batch_size, feature_dim] 特征矩阵

        Returns:
            orthogonal_loss: 正交损失
        """
        try:
            # 检查输入有效性
            if torch.isnan(features).any():
                return torch.tensor(0.0, device=features.device, requires_grad=True)

            # 中心化
            features_centered = features - features.mean(dim=0, keepdim=True)

            # 添加小的噪声防止零向量
            eps = 1e-8
            features_centered = features_centered + eps * torch.randn_like(features_centered)

            # 安全的归一化
            features_norm = features_centered / (torch.norm(features_centered, dim=0, keepdim=True) + eps)

            # 计算Gram矩阵
            gram_matrix = torch.mm(features_norm.T, features_norm)

            # 检查Gram矩阵有效性
            if torch.isnan(gram_matrix).any():
                return torch.tensor(0.0, device=features.device, requires_grad=True)

            # 正交损失：Gram矩阵与单位矩阵的差异
            identity_matrix = torch.eye(gram_matrix.size(0), device=gram_matrix.device)
            orthogonal_loss = torch.norm(gram_matrix - identity_matrix, p='fro') ** 2

            # 限制损失范围
            orthogonal_loss = torch.clamp(orthogonal_loss, 0, 100.0)

            return orthogonal_loss * self.orthogonal_weight

        except Exception as e:
            # 异常情况下返回零损失
            return torch.tensor(0.0, device=features.device, requires_grad=True)
    
    def compute_mutual_information_loss(self, identity_features, variation_features):
        """
        计算互信息损失的近似（数值稳定版本）
        使用简化的基于相关性的方法，避免协方差矩阵和logdet的数值问题

        Args:
            identity_features: [batch_size, identity_dim] 身份特征
            variation_features: [batch_size, variation_dim] 变化特征

        Returns:
            mi_loss: 互信息损失
        """
        try:
            # 检查输入有效性
            if torch.isnan(identity_features).any() or torch.isnan(variation_features).any():
                return torch.tensor(0.0, device=identity_features.device, requires_grad=True)

            batch_size = identity_features.size(0)

            # 如果批量太小，跳过互信息计算
            if batch_size < 3:
                return torch.tensor(0.0, device=identity_features.device, requires_grad=True)

            # 使用简化的互信息近似：基于特征相关性
            # 中心化特征
            identity_centered = identity_features - identity_features.mean(dim=0, keepdim=True)
            variation_centered = variation_features - variation_features.mean(dim=0, keepdim=True)

            # 添加小的正则化
            eps = 1e-6
            identity_centered = identity_centered + eps * torch.randn_like(identity_centered)
            variation_centered = variation_centered + eps * torch.randn_like(variation_centered)

            # 计算标准化的特征
            identity_std = torch.std(identity_centered, dim=0) + eps
            variation_std = torch.std(variation_centered, dim=0) + eps

            identity_normalized = identity_centered / identity_std
            variation_normalized = variation_centered / variation_std

            # 计算互相关系数
            correlation_matrix = torch.mm(identity_normalized.T, variation_normalized) / batch_size

            # 检查相关矩阵有效性
            if torch.isnan(correlation_matrix).any():
                return torch.tensor(0.0, device=identity_features.device, requires_grad=True)

            # 使用相关系数的平方和作为互信息的近似
            mi_approximation = torch.sum(correlation_matrix ** 2)

            # 限制损失范围
            mi_loss = torch.clamp(mi_approximation, 0, 10.0) * self.mutual_info_weight

            return mi_loss

        except Exception as e:
            # 异常情况下返回零损失
            return torch.tensor(0.0, device=identity_features.device, requires_grad=True)
    
    def compute_contrastive_loss(self, identity_features, variation_features, labels=None):
        """
        计算对比损失，确保同类样本的身份特征相似，变化特征不同（数值稳定版本）

        Args:
            identity_features: [batch_size, identity_dim] 身份特征
            variation_features: [batch_size, variation_dim] 变化特征
            labels: [batch_size] 类别标签（可选）

        Returns:
            contrastive_loss: 对比损失
        """
        try:
            # 检查输入有效性
            if torch.isnan(identity_features).any() or torch.isnan(variation_features).any():
                return torch.tensor(0.0, device=identity_features.device, requires_grad=True)

            # 批量太小时跳过对比损失
            if identity_features.size(0) < 2:
                return torch.tensor(0.0, device=identity_features.device, requires_grad=True)

            if labels is None:
                # 如果没有标签，使用自监督对比学习
                return self._compute_self_supervised_contrastive_loss(identity_features, variation_features)
            else:
                # 有监督对比学习
                return self._compute_supervised_contrastive_loss(identity_features, variation_features, labels)

        except Exception as e:
            # 异常情况下返回零损失
            return torch.tensor(0.0, device=identity_features.device, requires_grad=True)
    
    def _compute_self_supervised_contrastive_loss(self, identity_features, variation_features):
        """
        自监督对比损失：身份特征应该对变化不敏感
        """
        batch_size = identity_features.size(0)
        
        # 计算身份特征的相似性矩阵
        identity_sim = F.cosine_similarity(identity_features.unsqueeze(1), identity_features.unsqueeze(0), dim=2)
        
        # 计算变化特征的相似性矩阵
        variation_sim = F.cosine_similarity(variation_features.unsqueeze(1), variation_features.unsqueeze(0), dim=2)
        
        # 对比损失：身份相似但变化不同的样本对应该有高身份相似度和低变化相似度
        # 这里使用简化版本：最小化身份和变化相似性的相关性
        identity_sim_flat = identity_sim.view(-1)
        variation_sim_flat = variation_sim.view(-1)
        
        # 计算相关系数
        correlation = F.cosine_similarity(identity_sim_flat.unsqueeze(0), variation_sim_flat.unsqueeze(0), dim=1)
        
        contrastive_loss = correlation.abs() * self.contrastive_weight
        
        return contrastive_loss
    
    def _compute_supervised_contrastive_loss(self, identity_features, variation_features, labels):
        """
        有监督对比损失
        """
        batch_size = identity_features.size(0)
        
        # 创建正负样本掩码
        labels = labels.view(-1, 1)
        mask = torch.eq(labels, labels.T).float()
        
        # 计算身份特征相似性
        identity_sim = F.cosine_similarity(identity_features.unsqueeze(1), identity_features.unsqueeze(0), dim=2)
        identity_sim = identity_sim / self.temperature
        
        # 计算变化特征相似性
        variation_sim = F.cosine_similarity(variation_features.unsqueeze(1), variation_features.unsqueeze(0), dim=2)
        variation_sim = variation_sim / self.temperature
        
        # 对于同类样本：身份特征应该相似，变化特征应该不同
        positive_mask = mask * (1 - torch.eye(batch_size, device=mask.device))
        
        # 身份特征对比损失：同类样本应该相似
        identity_positive = (identity_sim * positive_mask).sum(dim=1) / (positive_mask.sum(dim=1) + 1e-8)
        identity_negative = (identity_sim * (1 - mask)).sum(dim=1) / ((1 - mask).sum(dim=1) + 1e-8)
        identity_contrastive = -torch.log(torch.exp(identity_positive) / (torch.exp(identity_positive) + torch.exp(identity_negative)) + 1e-8).mean()
        
        # 变化特征对比损失：同类样本的变化特征应该不同
        variation_positive = (variation_sim * positive_mask).sum(dim=1) / (positive_mask.sum(dim=1) + 1e-8)
        variation_contrastive = variation_positive.mean()  # 最小化同类样本的变化相似性
        
        contrastive_loss = (identity_contrastive + variation_contrastive) * self.contrastive_weight
        
        return contrastive_loss
    
    def forward(self, identity_features, variation_features, labels=None):
        """
        计算总的解耦损失（数值稳定版本）

        Args:
            identity_features: [batch_size, identity_dim] 身份特征
            variation_features: [batch_size, variation_dim] 变化特征
            labels: [batch_size] 类别标签（可选）

        Returns:
            total_loss: 总解耦损失
            loss_dict: 各项损失的详细信息
        """
        loss_dict = {}

        try:
            # 全面的输入检查
            if (identity_features is None or variation_features is None or
                torch.isnan(identity_features).any() or torch.isnan(variation_features).any() or
                identity_features.size(0) == 0 or variation_features.size(0) == 0):

                # 返回零损失
                zero_loss = torch.tensor(0.0, device=identity_features.device if identity_features is not None else torch.device('cpu'), requires_grad=True)
                loss_dict = {'decorrelation': 0.0, 'orthogonal': 0.0, 'mutual_info': 0.0, 'contrastive': 0.0, 'total': 0.0}
                return zero_loss, loss_dict

            # 去相关损失
            decorrelation_loss = self.compute_decorrelation_loss(identity_features, variation_features)
            loss_dict['decorrelation'] = decorrelation_loss.item() if not torch.isnan(decorrelation_loss) else 0.0

            # 正交损失
            identity_orthogonal_loss = self.compute_orthogonal_loss(identity_features)
            variation_orthogonal_loss = self.compute_orthogonal_loss(variation_features)
            orthogonal_loss = identity_orthogonal_loss + variation_orthogonal_loss
            loss_dict['orthogonal'] = orthogonal_loss.item() if not torch.isnan(orthogonal_loss) else 0.0

            # 互信息损失
            if identity_features.size(0) > 2:  # 需要至少3个样本来计算协方差
                mi_loss = self.compute_mutual_information_loss(identity_features, variation_features)
                loss_dict['mutual_info'] = mi_loss.item() if not torch.isnan(mi_loss) else 0.0
            else:
                mi_loss = torch.tensor(0.0, device=identity_features.device, requires_grad=True)
                loss_dict['mutual_info'] = 0.0

            # 对比损失
            if identity_features.size(0) > 1:
                contrastive_loss = self.compute_contrastive_loss(identity_features, variation_features, labels)
                loss_dict['contrastive'] = contrastive_loss.item() if not torch.isnan(contrastive_loss) else 0.0
            else:
                contrastive_loss = torch.tensor(0.0, device=identity_features.device, requires_grad=True)
                loss_dict['contrastive'] = 0.0

            # 总损失计算和NaN检查
            total_loss = decorrelation_loss + orthogonal_loss + mi_loss + contrastive_loss

            # 最终的NaN检查和限制
            if torch.isnan(total_loss) or torch.isinf(total_loss):
                total_loss = torch.tensor(0.0, device=identity_features.device, requires_grad=True)
                loss_dict['total'] = 0.0
            else:
                # 限制损失范围防止梯度爆炸
                total_loss = torch.clamp(total_loss, 0, 1000.0)
                loss_dict['total'] = total_loss.item()

            return total_loss, loss_dict

        except Exception as e:
            # 异常情况下返回零损失
            zero_loss = torch.tensor(0.0, device=identity_features.device if identity_features is not None else torch.device('cpu'), requires_grad=True)
            loss_dict = {'decorrelation': 0.0, 'orthogonal': 0.0, 'mutual_info': 0.0, 'contrastive': 0.0, 'total': 0.0}
            return zero_loss, loss_dict


class AdaptiveDisentanglementLoss(DisentanglementLossManager):
    """
    自适应解耦损失，根据训练进度调整各项损失的权重
    """
    def __init__(self, 
                 decorrelation_weight=1.0,
                 orthogonal_weight=0.1,
                 mutual_info_weight=0.5,
                 contrastive_weight=0.3,
                 temperature=0.1,
                 warmup_epochs=10,
                 total_epochs=100):
        super().__init__(decorrelation_weight, orthogonal_weight, mutual_info_weight, contrastive_weight, temperature)
        
        self.warmup_epochs = warmup_epochs
        self.total_epochs = total_epochs
        self.current_epoch = 0
        
        # 保存初始权重
        self.initial_decorrelation_weight = decorrelation_weight
        self.initial_orthogonal_weight = orthogonal_weight
        self.initial_mutual_info_weight = mutual_info_weight
        self.initial_contrastive_weight = contrastive_weight
    
    def update_epoch(self, epoch):
        """更新当前epoch，调整损失权重"""
        self.current_epoch = epoch
        
        # 计算权重调整因子
        if epoch < self.warmup_epochs:
            # 预热阶段：逐渐增加解耦损失权重
            factor = epoch / self.warmup_epochs
        else:
            # 正常训练阶段：保持权重或逐渐减少
            factor = 1.0 - 0.5 * (epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
            factor = max(factor, 0.1)  # 最小保持10%的权重
        
        # 更新权重
        self.decorrelation_weight = self.initial_decorrelation_weight * factor
        self.orthogonal_weight = self.initial_orthogonal_weight * factor
        self.mutual_info_weight = self.initial_mutual_info_weight * factor
        self.contrastive_weight = self.initial_contrastive_weight * factor
