2025-07-16 21:47:21,058 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-16 21:47:21,058 [trainer.py] => prefix: disentangled_reproduce
2025-07-16 21:47:21,059 [trainer.py] => dataset: cub
2025-07-16 21:47:21,059 [trainer.py] => memory_size: 0
2025-07-16 21:47:21,059 [trainer.py] => shuffle: True
2025-07-16 21:47:21,059 [trainer.py] => init_cls: 100
2025-07-16 21:47:21,059 [trainer.py] => increment: 10
2025-07-16 21:47:21,059 [trainer.py] => model_name: ranpac
2025-07-16 21:47:21,059 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:47:21,060 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:47:21,060 [trainer.py] => seed: 1993
2025-07-16 21:47:21,060 [trainer.py] => resume: False
2025-07-16 21:47:21,060 [trainer.py] => shot: 5
2025-07-16 21:47:21,060 [trainer.py] => use_simplecil: False
2025-07-16 21:47:21,060 [trainer.py] => tuned_epoch: 40
2025-07-16 21:47:21,060 [trainer.py] => init_lr: 0.01
2025-07-16 21:47:21,060 [trainer.py] => batch_size: 48
2025-07-16 21:47:21,061 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:47:21,061 [trainer.py] => min_lr: 0
2025-07-16 21:47:21,061 [trainer.py] => ffn_num: 64
2025-07-16 21:47:21,061 [trainer.py] => optimizer: sgd
2025-07-16 21:47:21,061 [trainer.py] => use_RP: True
2025-07-16 21:47:21,061 [trainer.py] => M: 10000
2025-07-16 21:47:21,061 [trainer.py] => fecam: False
2025-07-16 21:47:21,061 [trainer.py] => calibration: True
2025-07-16 21:47:21,061 [trainer.py] => knn_k: 5
2025-07-16 21:47:21,062 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:47:21,062 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:47:21,062 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:47:21,062 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:47:21,062 [trainer.py] => k_min: 3
2025-07-16 21:47:21,062 [trainer.py] => k_max: 21
2025-07-16 21:47:21,062 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:47:21,062 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:47:21,063 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-16 21:47:21,063 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:47:21,063 [trainer.py] => identity_bottleneck: 64
2025-07-16 21:47:21,063 [trainer.py] => variation_bottleneck: 64
2025-07-16 21:47:21,063 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-16 21:47:21,063 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-16 21:47:21,063 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-16 21:47:21,063 [trainer.py] => decorrelation_weight: 0.1
2025-07-16 21:47:21,064 [trainer.py] => orthogonal_weight: 0.01
2025-07-16 21:47:21,064 [trainer.py] => mutual_info_weight: 0.05
2025-07-16 21:47:21,064 [trainer.py] => contrastive_weight: 0.03
2025-07-16 21:47:21,064 [trainer.py] => disentangle_temperature: 0.1
2025-07-16 21:47:21,064 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-16 21:47:21,064 [trainer.py] => orthogonal_constraint: True
2025-07-16 21:47:21,064 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-16 21:47:21,064 [trainer.py] => variation_cov_weight: 0.8
2025-07-16 21:47:21,065 [trainer.py] => identity_similarity_weight: 1.0
2025-07-16 21:47:21,065 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-16 21:47:21,065 [trainer.py] => min_samples_for_cov: 5
2025-07-16 21:47:21,065 [trainer.py] => identity_similarity_metric: cosine
2025-07-16 21:47:21,065 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-16 21:47:21,065 [trainer.py] => identity_similarity_top_k: 5
2025-07-16 21:47:21,200 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:47:52,235 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-16 21:47:52,235 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:47:52,235 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:47:52,235 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:47:52,236 [trainer.py] => All params: 87578880
2025-07-16 21:47:52,237 [trainer.py] => Trainable params: 1780224
2025-07-16 21:47:53,646 [ranpac.py] => Learning on 0-100
2025-07-16 21:50:26,185 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-16 21:50:26,186 [trainer.py] => prefix: disentangled_reproduce
2025-07-16 21:50:26,187 [trainer.py] => dataset: cub
2025-07-16 21:50:26,187 [trainer.py] => memory_size: 0
2025-07-16 21:50:26,187 [trainer.py] => shuffle: True
2025-07-16 21:50:26,188 [trainer.py] => init_cls: 100
2025-07-16 21:50:26,188 [trainer.py] => increment: 10
2025-07-16 21:50:26,189 [trainer.py] => model_name: ranpac
2025-07-16 21:50:26,189 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:50:26,189 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:50:26,190 [trainer.py] => seed: 1993
2025-07-16 21:50:26,190 [trainer.py] => resume: False
2025-07-16 21:50:26,191 [trainer.py] => shot: 5
2025-07-16 21:50:26,191 [trainer.py] => use_simplecil: False
2025-07-16 21:50:26,191 [trainer.py] => tuned_epoch: 1
2025-07-16 21:50:26,192 [trainer.py] => init_lr: 0.01
2025-07-16 21:50:26,192 [trainer.py] => batch_size: 48
2025-07-16 21:50:26,192 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:50:26,193 [trainer.py] => min_lr: 0
2025-07-16 21:50:26,193 [trainer.py] => ffn_num: 64
2025-07-16 21:50:26,193 [trainer.py] => optimizer: sgd
2025-07-16 21:50:26,193 [trainer.py] => use_RP: True
2025-07-16 21:50:26,194 [trainer.py] => M: 10000
2025-07-16 21:50:26,194 [trainer.py] => fecam: False
2025-07-16 21:50:26,194 [trainer.py] => calibration: True
2025-07-16 21:50:26,195 [trainer.py] => knn_k: 5
2025-07-16 21:50:26,195 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:50:26,195 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:50:26,196 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:50:26,196 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:50:26,196 [trainer.py] => k_min: 3
2025-07-16 21:50:26,196 [trainer.py] => k_max: 21
2025-07-16 21:50:26,197 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:50:26,197 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:50:26,197 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-16 21:50:26,197 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:50:26,198 [trainer.py] => identity_bottleneck: 64
2025-07-16 21:50:26,198 [trainer.py] => variation_bottleneck: 64
2025-07-16 21:50:26,198 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-16 21:50:26,199 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-16 21:50:26,199 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-16 21:50:26,199 [trainer.py] => decorrelation_weight: 0.1
2025-07-16 21:50:26,199 [trainer.py] => orthogonal_weight: 0.01
2025-07-16 21:50:26,200 [trainer.py] => mutual_info_weight: 0.05
2025-07-16 21:50:26,200 [trainer.py] => contrastive_weight: 0.03
2025-07-16 21:50:26,200 [trainer.py] => disentangle_temperature: 0.1
2025-07-16 21:50:26,201 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-16 21:50:26,201 [trainer.py] => orthogonal_constraint: True
2025-07-16 21:50:26,201 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-16 21:50:26,204 [trainer.py] => variation_cov_weight: 0.8
2025-07-16 21:50:26,204 [trainer.py] => identity_similarity_weight: 1.0
2025-07-16 21:50:26,204 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-16 21:50:26,204 [trainer.py] => min_samples_for_cov: 5
2025-07-16 21:50:26,204 [trainer.py] => identity_similarity_metric: cosine
2025-07-16 21:50:26,204 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-16 21:50:26,204 [trainer.py] => identity_similarity_top_k: 5
2025-07-16 21:50:26,482 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:50:46,691 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-16 21:50:46,691 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:50:46,691 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:50:46,691 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:50:46,692 [trainer.py] => All params: 87578880
2025-07-16 21:50:46,693 [trainer.py] => Trainable params: 1780224
2025-07-16 21:50:48,122 [ranpac.py] => Learning on 0-100
2025-07-16 21:51:00,963 [ranpac.py] => [Disentangled Training] Error in disentangled forward pass: CUDA out of memory. Tried to allocate 112.00 MiB (GPU 0; 21.99 GiB total capacity; 2.37 GiB already allocated; 44.81 MiB free; 2.51 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF, falling back to normal training
2025-07-17 12:03:01,621 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-17 12:03:01,622 [trainer.py] => prefix: disentangled_reproduce
2025-07-17 12:03:01,622 [trainer.py] => dataset: cub
2025-07-17 12:03:01,623 [trainer.py] => memory_size: 0
2025-07-17 12:03:01,623 [trainer.py] => shuffle: True
2025-07-17 12:03:01,624 [trainer.py] => init_cls: 100
2025-07-17 12:03:01,624 [trainer.py] => increment: 10
2025-07-17 12:03:01,625 [trainer.py] => model_name: ranpac
2025-07-17 12:03:01,625 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-17 12:03:01,625 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-17 12:03:01,626 [trainer.py] => seed: 1993
2025-07-17 12:03:01,626 [trainer.py] => resume: False
2025-07-17 12:03:01,627 [trainer.py] => shot: 5
2025-07-17 12:03:01,627 [trainer.py] => use_simplecil: False
2025-07-17 12:03:01,627 [trainer.py] => tuned_epoch: 1
2025-07-17 12:03:01,628 [trainer.py] => init_lr: 0.01
2025-07-17 12:03:01,628 [trainer.py] => batch_size: 48
2025-07-17 12:03:01,629 [trainer.py] => weight_decay: 0.0005
2025-07-17 12:03:01,629 [trainer.py] => min_lr: 0
2025-07-17 12:03:01,629 [trainer.py] => ffn_num: 64
2025-07-17 12:03:01,630 [trainer.py] => optimizer: sgd
2025-07-17 12:03:01,630 [trainer.py] => use_RP: True
2025-07-17 12:03:01,630 [trainer.py] => M: 10000
2025-07-17 12:03:01,631 [trainer.py] => fecam: False
2025-07-17 12:03:01,631 [trainer.py] => calibration: True
2025-07-17 12:03:01,632 [trainer.py] => knn_k: 5
2025-07-17 12:03:01,632 [trainer.py] => knn_distance_metric: cosine
2025-07-17 12:03:01,637 [trainer.py] => knn_weight_decay: 0.1
2025-07-17 12:03:01,637 [trainer.py] => knn_adaptive_k: True
2025-07-17 12:03:01,637 [trainer.py] => knn_temperature: 16.0
2025-07-17 12:03:01,637 [trainer.py] => k_min: 3
2025-07-17 12:03:01,637 [trainer.py] => k_max: 21
2025-07-17 12:03:01,637 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-17 12:03:01,637 [trainer.py] => cosine_temperature: 16.0
2025-07-17 12:03:01,638 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-17 12:03:01,638 [trainer.py] => use_disentangled_adapter: True
2025-07-17 12:03:01,638 [trainer.py] => identity_bottleneck: 64
2025-07-17 12:03:01,638 [trainer.py] => variation_bottleneck: 64
2025-07-17 12:03:01,638 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-17 12:03:01,638 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-17 12:03:01,638 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-17 12:03:01,638 [trainer.py] => decorrelation_weight: 0.1
2025-07-17 12:03:01,638 [trainer.py] => orthogonal_weight: 0.01
2025-07-17 12:03:01,638 [trainer.py] => mutual_info_weight: 0.05
2025-07-17 12:03:01,639 [trainer.py] => contrastive_weight: 0.03
2025-07-17 12:03:01,639 [trainer.py] => disentangle_temperature: 0.1
2025-07-17 12:03:01,639 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-17 12:03:01,639 [trainer.py] => orthogonal_constraint: True
2025-07-17 12:03:01,639 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-17 12:03:01,639 [trainer.py] => variation_cov_weight: 0.8
2025-07-17 12:03:01,639 [trainer.py] => identity_similarity_weight: 1.0
2025-07-17 12:03:01,639 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-17 12:03:01,639 [trainer.py] => min_samples_for_cov: 5
2025-07-17 12:03:01,639 [trainer.py] => identity_similarity_metric: cosine
2025-07-17 12:03:01,639 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-17 12:03:01,640 [trainer.py] => identity_similarity_top_k: 5
2025-07-17 12:03:01,775 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-17 12:03:36,374 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-17 12:03:36,375 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-17 12:03:36,375 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-17 12:03:36,376 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-17 12:03:36,377 [trainer.py] => All params: 87578880
2025-07-17 12:03:36,379 [trainer.py] => Trainable params: 1780224
2025-07-17 12:03:46,646 [ranpac.py] => Learning on 0-100
2025-07-17 12:05:38,824 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.04, Test_accy 8.49
2025-07-17 12:05:59,547 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-17 12:06:28,678 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-17 12:06:29,215 [ranpac.py] => [Dynamic-K] Base similarity range: [7.0716, 10.8336]
2025-07-17 12:06:29,215 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-17 12:06:29,215 [trainer.py] => No NME accuracy.
2025-07-17 12:06:29,215 [trainer.py] => CNN: {'total': 90.57, '00-99': 90.57, 'old': 0, 'new': 90.57}
2025-07-17 12:06:29,215 [trainer.py] => CNN HM: [0.0]
2025-07-17 12:06:29,215 [trainer.py] => CNN top1 curve: [90.57]
2025-07-17 12:06:29,215 [trainer.py] => Average Accuracy (CNN): 90.57 

2025-07-17 12:06:29,216 [trainer.py] => All params: 88578881
2025-07-17 12:06:29,217 [trainer.py] => Trainable params: 1780225
2025-07-17 12:06:29,305 [ranpac.py] => Learning on 100-110
2025-07-17 12:06:33,278 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:06:33,320 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:06:33,347 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 6, 15, 13, 10, 6]
2025-07-17 12:06:33,347 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 6, 15, 13, 10, 6]
2025-07-17 12:06:34,270 [ranpac.py] => [KNN] task 1, weight sparsity: 0.889, distance_metric: cosine
2025-07-17 12:06:34,270 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:06:41,536 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:06:54,530 [trainer.py] => No NME accuracy.
2025-07-17 12:06:54,530 [trainer.py] => CNN: {'total': 89.15, '00-99': 89.67, '100-109': 84.12, 'old': 89.67, 'new': 84.12}
2025-07-17 12:06:54,531 [trainer.py] => CNN HM: [0.0, 86.806]
2025-07-17 12:06:54,531 [trainer.py] => CNN top1 curve: [90.57, 89.15]
2025-07-17 12:06:54,531 [trainer.py] => Average Accuracy (CNN): 89.86 

2025-07-17 12:06:54,533 [trainer.py] => All params: 88678881
2025-07-17 12:06:54,535 [trainer.py] => Trainable params: 2880225
2025-07-17 12:06:54,550 [ranpac.py] => Learning on 110-120
2025-07-17 12:06:57,181 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:06:57,194 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:06:57,196 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 9, 9, 17, 17, 11, 5, 15, 16]
2025-07-17 12:06:57,196 [ranpac.py] => [KNN] task 2, dynamic K values: [4, 12, 9, 9, 17, 17, 11, 5, 15, 16]
2025-07-17 12:06:57,197 [ranpac.py] => [KNN] task 2, weight sparsity: 0.885, distance_metric: cosine
2025-07-17 12:06:57,197 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:06:58,995 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:07:12,082 [trainer.py] => No NME accuracy.
2025-07-17 12:07:12,083 [trainer.py] => CNN: {'total': 87.62, '00-99': 90.02, '100-109': 84.46, '110-119': 66.55, 'old': 89.5, 'new': 66.55}
2025-07-17 12:07:12,083 [trainer.py] => CNN HM: [0.0, 86.806, 76.337]
2025-07-17 12:07:12,083 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62]
2025-07-17 12:07:12,083 [trainer.py] => Average Accuracy (CNN): 89.11333333333334 

2025-07-17 12:07:12,084 [trainer.py] => All params: 88778881
2025-07-17 12:07:12,085 [trainer.py] => Trainable params: 2980225
2025-07-17 12:07:12,100 [ranpac.py] => Learning on 120-130
2025-07-17 12:07:16,386 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:07:16,403 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:07:16,404 [ranpac.py] => [Dynamic-K] Computed K values: [10, 10, 10, 16, 6, 6, 6, 7, 8, 8]
2025-07-17 12:07:16,404 [ranpac.py] => [KNN] task 3, dynamic K values: [10, 10, 10, 16, 6, 6, 6, 7, 8, 8]
2025-07-17 12:07:16,405 [ranpac.py] => [KNN] task 3, weight sparsity: 0.913, distance_metric: cosine
2025-07-17 12:07:16,405 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:07:18,147 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:07:32,613 [trainer.py] => No NME accuracy.
2025-07-17 12:07:32,613 [trainer.py] => CNN: {'total': 85.43, '00-99': 89.12, '100-109': 84.12, '110-119': 66.55, '120-129': 68.62, 'old': 86.84, 'new': 68.62}
2025-07-17 12:07:32,613 [trainer.py] => CNN HM: [0.0, 86.806, 76.337, 76.662]
2025-07-17 12:07:32,613 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62, 85.43]
2025-07-17 12:07:32,613 [trainer.py] => Average Accuracy (CNN): 88.19250000000001 

2025-07-17 12:07:32,614 [trainer.py] => All params: 88878881
2025-07-17 12:07:32,615 [trainer.py] => Trainable params: 3080225
2025-07-17 12:07:32,628 [ranpac.py] => Learning on 130-140
2025-07-17 12:07:36,477 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:07:36,495 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:07:36,497 [ranpac.py] => [Dynamic-K] Computed K values: [9, 15, 10, 11, 14, 6, 9, 8, 11, 10]
2025-07-17 12:07:36,497 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 15, 10, 11, 14, 6, 9, 8, 11, 10]
2025-07-17 12:07:36,498 [ranpac.py] => [KNN] task 4, weight sparsity: 0.897, distance_metric: cosine
2025-07-17 12:07:36,498 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:07:38,062 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:07:53,664 [trainer.py] => No NME accuracy.
2025-07-17 12:07:53,665 [trainer.py] => CNN: {'total': 84.82, '00-99': 89.05, '100-109': 84.12, '110-119': 67.25, '120-129': 65.52, '130-139': 80.0, 'old': 85.19, 'new': 80.0}
2025-07-17 12:07:53,665 [trainer.py] => CNN HM: [0.0, 86.806, 76.337, 76.662, 82.513]
2025-07-17 12:07:53,665 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62, 85.43, 84.82]
2025-07-17 12:07:53,665 [trainer.py] => Average Accuracy (CNN): 87.518 

2025-07-17 12:07:53,666 [trainer.py] => All params: 88978881
2025-07-17 12:07:53,667 [trainer.py] => Trainable params: 3180225
2025-07-17 12:07:53,681 [ranpac.py] => Learning on 140-150
2025-07-17 12:07:56,232 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:07:56,248 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:07:56,250 [ranpac.py] => [Dynamic-K] Computed K values: [11, 14, 12, 7, 8, 12, 11, 4, 8, 9]
2025-07-17 12:07:56,250 [ranpac.py] => [KNN] task 5, dynamic K values: [11, 14, 12, 7, 8, 12, 11, 4, 8, 9]
2025-07-17 12:07:56,251 [ranpac.py] => [KNN] task 5, weight sparsity: 0.904, distance_metric: cosine
2025-07-17 12:07:56,251 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:07:58,023 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:08:13,620 [trainer.py] => No NME accuracy.
2025-07-17 12:08:13,620 [trainer.py] => CNN: {'total': 83.07, '00-99': 88.35, '100-109': 84.8, '110-119': 65.85, '120-129': 64.83, '130-139': 80.0, '140-149': 66.19, 'old': 84.23, 'new': 66.19}
2025-07-17 12:08:13,620 [trainer.py] => CNN HM: [0.0, 86.806, 76.337, 76.662, 82.513, 74.128]
2025-07-17 12:08:13,620 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62, 85.43, 84.82, 83.07]
2025-07-17 12:08:13,620 [trainer.py] => Average Accuracy (CNN): 86.77666666666669 

2025-07-17 12:08:13,622 [trainer.py] => All params: 89078881
2025-07-17 12:08:13,623 [trainer.py] => Trainable params: 3280225
2025-07-17 12:08:13,641 [ranpac.py] => Learning on 150-160
2025-07-17 12:08:16,460 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:08:16,477 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:08:16,479 [ranpac.py] => [Dynamic-K] Computed K values: [8, 12, 8, 7, 9, 9, 8, 7, 18, 9]
2025-07-17 12:08:16,479 [ranpac.py] => [KNN] task 6, dynamic K values: [8, 12, 8, 7, 9, 9, 8, 7, 18, 9]
2025-07-17 12:08:16,479 [ranpac.py] => [KNN] task 6, weight sparsity: 0.905, distance_metric: cosine
2025-07-17 12:08:16,479 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:08:18,685 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:08:35,188 [trainer.py] => No NME accuracy.
2025-07-17 12:08:35,188 [trainer.py] => CNN: {'total': 82.62, '00-99': 88.35, '100-109': 83.78, '110-119': 65.14, '120-129': 64.48, '130-139': 80.0, '140-149': 65.83, '150-159': 78.42, 'old': 82.91, 'new': 78.42}
2025-07-17 12:08:35,189 [trainer.py] => CNN HM: [0.0, 86.806, 76.337, 76.662, 82.513, 74.128, 80.603]
2025-07-17 12:08:35,189 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62, 85.43, 84.82, 83.07, 82.62]
2025-07-17 12:08:35,189 [trainer.py] => Average Accuracy (CNN): 86.18285714285716 

2025-07-17 12:08:35,191 [trainer.py] => All params: 89178881
2025-07-17 12:08:35,193 [trainer.py] => Trainable params: 3380225
2025-07-17 12:08:35,211 [ranpac.py] => Learning on 160-170
2025-07-17 12:08:39,094 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:08:39,116 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:08:39,118 [ranpac.py] => [Dynamic-K] Computed K values: [15, 13, 13, 6, 12, 8, 10, 6, 11, 7]
2025-07-17 12:08:39,118 [ranpac.py] => [KNN] task 7, dynamic K values: [15, 13, 13, 6, 12, 8, 10, 6, 11, 7]
2025-07-17 12:08:39,119 [ranpac.py] => [KNN] task 7, weight sparsity: 0.899, distance_metric: cosine
2025-07-17 12:08:39,119 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:08:41,065 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:08:59,346 [trainer.py] => No NME accuracy.
2025-07-17 12:08:59,346 [trainer.py] => CNN: {'total': 81.64, '00-99': 88.15, '100-109': 84.12, '110-119': 64.08, '120-129': 64.83, '130-139': 78.62, '140-149': 60.43, '150-159': 78.08, '160-169': 75.5, 'old': 82.04, 'new': 75.5}
2025-07-17 12:08:59,346 [trainer.py] => CNN HM: [0.0, 86.806, 76.337, 76.662, 82.513, 74.128, 80.603, 78.634]
2025-07-17 12:08:59,346 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62, 85.43, 84.82, 83.07, 82.62, 81.64]
2025-07-17 12:08:59,346 [trainer.py] => Average Accuracy (CNN): 85.61500000000001 

2025-07-17 12:08:59,347 [trainer.py] => All params: 89278881
2025-07-17 12:08:59,348 [trainer.py] => Trainable params: 3480225
2025-07-17 12:08:59,363 [ranpac.py] => Learning on 170-180
2025-07-17 12:09:01,187 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:09:01,200 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:09:01,202 [ranpac.py] => [Dynamic-K] Computed K values: [13, 8, 10, 7, 9, 7, 4, 10, 15, 6]
2025-07-17 12:09:01,202 [ranpac.py] => [KNN] task 8, dynamic K values: [13, 8, 10, 7, 9, 7, 4, 10, 15, 6]
2025-07-17 12:09:01,203 [ranpac.py] => [KNN] task 8, weight sparsity: 0.911, distance_metric: cosine
2025-07-17 12:09:01,203 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:09:02,767 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:09:22,968 [trainer.py] => No NME accuracy.
2025-07-17 12:09:22,968 [trainer.py] => CNN: {'total': 81.14, '00-99': 87.87, '100-109': 83.78, '110-119': 65.14, '120-129': 65.52, '130-139': 77.59, '140-149': 58.99, '150-159': 77.05, '160-169': 75.5, '170-179': 77.52, 'old': 81.36, 'new': 77.52}
2025-07-17 12:09:22,968 [trainer.py] => CNN HM: [0.0, 86.806, 76.337, 76.662, 82.513, 74.128, 80.603, 78.634, 79.394]
2025-07-17 12:09:22,968 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62, 85.43, 84.82, 83.07, 82.62, 81.64, 81.14]
2025-07-17 12:09:22,968 [trainer.py] => Average Accuracy (CNN): 85.11777777777779 

2025-07-17 12:09:22,970 [trainer.py] => All params: 89378881
2025-07-17 12:09:22,971 [trainer.py] => Trainable params: 3580225
2025-07-17 12:09:22,992 [ranpac.py] => Learning on 180-190
2025-07-17 12:09:25,952 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:09:25,968 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:09:25,970 [ranpac.py] => [Dynamic-K] Computed K values: [14, 13, 9, 11, 8, 9, 18, 13, 15, 9]
2025-07-17 12:09:25,970 [ranpac.py] => [KNN] task 9, dynamic K values: [14, 13, 9, 11, 8, 9, 18, 13, 15, 9]
2025-07-17 12:09:25,971 [ranpac.py] => [KNN] task 9, weight sparsity: 0.881, distance_metric: cosine
2025-07-17 12:09:25,971 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:09:28,226 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:09:49,157 [trainer.py] => No NME accuracy.
2025-07-17 12:09:49,157 [trainer.py] => CNN: {'total': 80.39, '00-99': 87.8, '100-109': 84.12, '110-119': 65.49, '120-129': 63.45, '130-139': 74.48, '140-149': 58.27, '150-159': 77.74, '160-169': 75.17, '170-179': 77.85, '180-189': 72.13, 'old': 80.85, 'new': 72.13}
2025-07-17 12:09:49,157 [trainer.py] => CNN HM: [0.0, 86.806, 76.337, 76.662, 82.513, 74.128, 80.603, 78.634, 79.394, 76.241]
2025-07-17 12:09:49,158 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62, 85.43, 84.82, 83.07, 82.62, 81.64, 81.14, 80.39]
2025-07-17 12:09:49,158 [trainer.py] => Average Accuracy (CNN): 84.64500000000001 

2025-07-17 12:09:49,159 [trainer.py] => All params: 89478881
2025-07-17 12:09:49,160 [trainer.py] => Trainable params: 3680225
2025-07-17 12:09:49,181 [ranpac.py] => Learning on 190-200
2025-07-17 12:09:51,848 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 12:09:51,866 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 12:09:51,868 [ranpac.py] => [Dynamic-K] Computed K values: [6, 10, 11, 7, 10, 9, 8, 6, 12, 8]
2025-07-17 12:09:51,868 [ranpac.py] => [KNN] task 10, dynamic K values: [6, 10, 11, 7, 10, 9, 8, 6, 12, 8]
2025-07-17 12:09:51,869 [ranpac.py] => [KNN] task 10, weight sparsity: 0.913, distance_metric: cosine
2025-07-17 12:09:51,869 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-17 12:09:54,094 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 12:10:14,367 [trainer.py] => No NME accuracy.
2025-07-17 12:10:14,368 [trainer.py] => CNN: {'total': 79.65, '00-99': 87.42, '100-109': 84.8, '110-119': 65.49, '120-129': 65.17, '130-139': 75.52, '140-149': 58.63, '150-159': 77.74, '160-169': 75.17, '170-179': 76.85, '180-189': 72.13, '190-199': 66.89, 'old': 80.34, 'new': 66.89}
2025-07-17 12:10:14,368 [trainer.py] => CNN HM: [0.0, 86.806, 76.337, 76.662, 82.513, 74.128, 80.603, 78.634, 79.394, 76.241, 73.001]
2025-07-17 12:10:14,368 [trainer.py] => CNN top1 curve: [90.57, 89.15, 87.62, 85.43, 84.82, 83.07, 82.62, 81.64, 81.14, 80.39, 79.65]
2025-07-17 12:10:14,368 [trainer.py] => Average Accuracy (CNN): 84.19090909090909 

2025-07-17 12:10:14,369 [trainer.py] => Forgetting (CNN): 2.2410000000000005
2025-07-17 13:05:02,587 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-17 13:05:02,590 [trainer.py] => prefix: disentangled_reproduce
2025-07-17 13:05:02,591 [trainer.py] => dataset: cub
2025-07-17 13:05:02,591 [trainer.py] => memory_size: 0
2025-07-17 13:05:02,592 [trainer.py] => shuffle: True
2025-07-17 13:05:02,592 [trainer.py] => init_cls: 100
2025-07-17 13:05:02,592 [trainer.py] => increment: 10
2025-07-17 13:05:02,593 [trainer.py] => model_name: ranpac
2025-07-17 13:05:02,593 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-17 13:05:02,594 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-17 13:05:02,594 [trainer.py] => seed: 1993
2025-07-17 13:05:02,594 [trainer.py] => resume: False
2025-07-17 13:05:02,595 [trainer.py] => shot: 5
2025-07-17 13:05:02,595 [trainer.py] => use_simplecil: False
2025-07-17 13:05:02,596 [trainer.py] => tuned_epoch: 1
2025-07-17 13:05:02,596 [trainer.py] => init_lr: 0.01
2025-07-17 13:05:02,597 [trainer.py] => batch_size: 48
2025-07-17 13:05:02,597 [trainer.py] => weight_decay: 0.0005
2025-07-17 13:05:02,597 [trainer.py] => min_lr: 0
2025-07-17 13:05:02,599 [trainer.py] => ffn_num: 64
2025-07-17 13:05:02,599 [trainer.py] => optimizer: sgd
2025-07-17 13:05:02,599 [trainer.py] => use_RP: True
2025-07-17 13:05:02,599 [trainer.py] => M: 10000
2025-07-17 13:05:02,599 [trainer.py] => fecam: False
2025-07-17 13:05:02,599 [trainer.py] => calibration: True
2025-07-17 13:05:02,599 [trainer.py] => knn_k: 5
2025-07-17 13:05:02,599 [trainer.py] => knn_distance_metric: cosine
2025-07-17 13:05:02,600 [trainer.py] => knn_weight_decay: 0.1
2025-07-17 13:05:02,600 [trainer.py] => knn_adaptive_k: True
2025-07-17 13:05:02,600 [trainer.py] => knn_temperature: 16.0
2025-07-17 13:05:02,600 [trainer.py] => k_min: 3
2025-07-17 13:05:02,600 [trainer.py] => k_max: 21
2025-07-17 13:05:02,600 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-17 13:05:02,600 [trainer.py] => cosine_temperature: 16.0
2025-07-17 13:05:02,600 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-17 13:05:02,601 [trainer.py] => use_disentangled_adapter: True
2025-07-17 13:05:02,601 [trainer.py] => identity_bottleneck: 64
2025-07-17 13:05:02,601 [trainer.py] => variation_bottleneck: 64
2025-07-17 13:05:02,601 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-17 13:05:02,601 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-17 13:05:02,601 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-17 13:05:02,601 [trainer.py] => decorrelation_weight: 0.1
2025-07-17 13:05:02,601 [trainer.py] => orthogonal_weight: 0.01
2025-07-17 13:05:02,601 [trainer.py] => mutual_info_weight: 0.05
2025-07-17 13:05:02,601 [trainer.py] => contrastive_weight: 0.03
2025-07-17 13:05:02,602 [trainer.py] => disentangle_temperature: 0.1
2025-07-17 13:05:02,602 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-17 13:05:02,602 [trainer.py] => orthogonal_constraint: True
2025-07-17 13:05:02,602 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-17 13:05:02,602 [trainer.py] => variation_cov_weight: 0.8
2025-07-17 13:05:02,602 [trainer.py] => identity_similarity_weight: 1.0
2025-07-17 13:05:02,602 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-17 13:05:02,602 [trainer.py] => min_samples_for_cov: 5
2025-07-17 13:05:02,602 [trainer.py] => identity_similarity_metric: cosine
2025-07-17 13:05:02,603 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-17 13:05:02,603 [trainer.py] => identity_similarity_top_k: 5
2025-07-17 13:05:02,720 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-17 13:05:49,259 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-17 13:05:49,260 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-17 13:05:49,260 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-17 13:05:49,260 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-17 13:05:49,261 [trainer.py] => All params: 87578880
2025-07-17 13:05:49,262 [trainer.py] => Trainable params: 1780224
2025-07-17 13:06:24,907 [ranpac.py] => Learning on 0-100
2025-07-17 13:08:07,745 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.04, Test_accy 8.49
2025-07-17 13:08:21,755 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-17 13:08:42,319 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-17 13:08:42,984 [ranpac.py] => [Dynamic-K] Base similarity range: [7.0716, 10.8336]
2025-07-17 13:08:42,985 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-17 13:08:42,985 [trainer.py] => No NME accuracy.
2025-07-17 13:08:42,985 [trainer.py] => CNN: {'total': 90.57, '00-99': 90.57, 'old': 0, 'new': 90.57}
2025-07-17 13:08:42,985 [trainer.py] => CNN HM: [0.0]
2025-07-17 13:08:42,985 [trainer.py] => CNN top1 curve: [90.57]
2025-07-17 13:08:42,985 [trainer.py] => Average Accuracy (CNN): 90.57 

2025-07-17 13:08:42,987 [trainer.py] => All params: 88578881
2025-07-17 13:08:42,988 [trainer.py] => Trainable params: 1780225
2025-07-17 13:08:43,019 [ranpac.py] => Learning on 100-110
2025-07-17 13:08:48,071 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:08:48,111 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:08:48,367 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:08:48,368 [ranpac.py] => [Disentangled KNN] task 1, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:08:48,805 [ranpac.py] => [Disentangled KNN] task 1, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:08:48,805 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:09:03,370 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:09:18,373 [trainer.py] => No NME accuracy.
2025-07-17 13:09:18,374 [trainer.py] => CNN: {'total': 89.06, '00-99': 89.74, '100-109': 82.43, 'old': 89.74, 'new': 82.43}
2025-07-17 13:09:18,374 [trainer.py] => CNN HM: [0.0, 85.93]
2025-07-17 13:09:18,375 [trainer.py] => CNN top1 curve: [90.57, 89.06]
2025-07-17 13:09:18,375 [trainer.py] => Average Accuracy (CNN): 89.815 

2025-07-17 13:09:18,377 [trainer.py] => All params: 88678881
2025-07-17 13:09:18,378 [trainer.py] => Trainable params: 2880225
2025-07-17 13:09:18,407 [ranpac.py] => Learning on 110-120
2025-07-17 13:09:21,076 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:09:21,175 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:09:21,300 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:09:21,300 [ranpac.py] => [Disentangled KNN] task 2, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:09:21,301 [ranpac.py] => [Disentangled KNN] task 2, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:09:21,301 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:09:24,632 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:11:48,386 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-17 13:11:48,387 [trainer.py] => prefix: disentangled_reproduce
2025-07-17 13:11:48,387 [trainer.py] => dataset: cub
2025-07-17 13:11:48,387 [trainer.py] => memory_size: 0
2025-07-17 13:11:48,388 [trainer.py] => shuffle: True
2025-07-17 13:11:48,388 [trainer.py] => init_cls: 100
2025-07-17 13:11:48,388 [trainer.py] => increment: 10
2025-07-17 13:11:48,388 [trainer.py] => model_name: ranpac
2025-07-17 13:11:48,388 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-17 13:11:48,388 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-17 13:11:48,388 [trainer.py] => seed: 1993
2025-07-17 13:11:48,389 [trainer.py] => resume: False
2025-07-17 13:11:48,389 [trainer.py] => shot: 5
2025-07-17 13:11:48,389 [trainer.py] => use_simplecil: False
2025-07-17 13:11:48,389 [trainer.py] => tuned_epoch: 1
2025-07-17 13:11:48,389 [trainer.py] => init_lr: 0.01
2025-07-17 13:11:48,389 [trainer.py] => batch_size: 48
2025-07-17 13:11:48,389 [trainer.py] => weight_decay: 0.0005
2025-07-17 13:11:48,389 [trainer.py] => min_lr: 0
2025-07-17 13:11:48,390 [trainer.py] => ffn_num: 64
2025-07-17 13:11:48,390 [trainer.py] => optimizer: sgd
2025-07-17 13:11:48,390 [trainer.py] => use_RP: True
2025-07-17 13:11:48,390 [trainer.py] => M: 10000
2025-07-17 13:11:48,390 [trainer.py] => fecam: False
2025-07-17 13:11:48,390 [trainer.py] => calibration: True
2025-07-17 13:11:48,390 [trainer.py] => knn_k: 5
2025-07-17 13:11:48,390 [trainer.py] => knn_distance_metric: cosine
2025-07-17 13:11:48,390 [trainer.py] => knn_weight_decay: 0.1
2025-07-17 13:11:48,391 [trainer.py] => knn_adaptive_k: True
2025-07-17 13:11:48,391 [trainer.py] => knn_temperature: 16.0
2025-07-17 13:11:48,391 [trainer.py] => k_min: 3
2025-07-17 13:11:48,391 [trainer.py] => k_max: 21
2025-07-17 13:11:48,391 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-17 13:11:48,391 [trainer.py] => cosine_temperature: 16.0
2025-07-17 13:11:48,391 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-17 13:11:48,391 [trainer.py] => use_disentangled_adapter: True
2025-07-17 13:11:48,392 [trainer.py] => identity_bottleneck: 64
2025-07-17 13:11:48,392 [trainer.py] => variation_bottleneck: 64
2025-07-17 13:11:48,392 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-17 13:11:48,392 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-17 13:11:48,392 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-17 13:11:48,392 [trainer.py] => decorrelation_weight: 0.1
2025-07-17 13:11:48,392 [trainer.py] => orthogonal_weight: 0.01
2025-07-17 13:11:48,392 [trainer.py] => mutual_info_weight: 0.05
2025-07-17 13:11:48,393 [trainer.py] => contrastive_weight: 0.03
2025-07-17 13:11:48,393 [trainer.py] => disentangle_temperature: 0.1
2025-07-17 13:11:48,393 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-17 13:11:48,393 [trainer.py] => orthogonal_constraint: True
2025-07-17 13:11:48,393 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-17 13:11:48,393 [trainer.py] => variation_cov_weight: 0.8
2025-07-17 13:11:48,393 [trainer.py] => identity_similarity_weight: 1.0
2025-07-17 13:11:48,393 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-17 13:11:48,393 [trainer.py] => min_samples_for_cov: 5
2025-07-17 13:11:48,394 [trainer.py] => identity_similarity_metric: cosine
2025-07-17 13:11:48,394 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-17 13:11:48,394 [trainer.py] => identity_similarity_top_k: 5
2025-07-17 13:11:48,534 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-17 13:12:29,659 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-17 13:12:29,660 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-17 13:12:29,660 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-17 13:12:29,660 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-17 13:12:29,661 [trainer.py] => All params: 87578880
2025-07-17 13:12:29,662 [trainer.py] => Trainable params: 1780224
2025-07-17 13:12:31,854 [ranpac.py] => Learning on 0-100
2025-07-17 13:13:29,807 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.04, Test_accy 8.49
2025-07-17 13:13:46,127 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-17 13:14:02,932 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-17 13:14:03,476 [ranpac.py] => [Dynamic-K] Base similarity range: [7.0716, 10.8336]
2025-07-17 13:14:03,476 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-17 13:14:03,476 [trainer.py] => No NME accuracy.
2025-07-17 13:14:03,476 [trainer.py] => CNN: {'total': 90.57, '00-99': 90.57, 'old': 0, 'new': 90.57}
2025-07-17 13:14:03,476 [trainer.py] => CNN HM: [0.0]
2025-07-17 13:14:03,476 [trainer.py] => CNN top1 curve: [90.57]
2025-07-17 13:14:03,476 [trainer.py] => Average Accuracy (CNN): 90.57 

2025-07-17 13:14:03,477 [trainer.py] => All params: 88578881
2025-07-17 13:14:03,478 [trainer.py] => Trainable params: 1780225
2025-07-17 13:14:03,491 [ranpac.py] => Learning on 100-110
2025-07-17 13:14:06,970 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:14:07,219 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:14:07,502 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:14:07,502 [ranpac.py] => [Disentangled KNN] task 1, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:14:07,503 [ranpac.py] => [Disentangled KNN] task 1, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:14:07,504 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:14:07,646 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:14:10,765 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:14:22,666 [trainer.py] => No NME accuracy.
2025-07-17 13:14:22,666 [trainer.py] => CNN: {'total': 89.34, '00-99': 89.95, '100-109': 83.45, 'old': 89.95, 'new': 83.45}
2025-07-17 13:14:22,666 [trainer.py] => CNN HM: [0.0, 86.578]
2025-07-17 13:14:22,667 [trainer.py] => CNN top1 curve: [90.57, 89.34]
2025-07-17 13:14:22,667 [trainer.py] => Average Accuracy (CNN): 89.955 

2025-07-17 13:14:22,668 [trainer.py] => All params: 88678881
2025-07-17 13:14:22,669 [trainer.py] => Trainable params: 2880225
2025-07-17 13:14:22,687 [ranpac.py] => Learning on 110-120
2025-07-17 13:14:25,225 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:14:25,241 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:14:25,493 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:14:25,494 [ranpac.py] => [Disentangled KNN] task 2, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:14:25,494 [ranpac.py] => [Disentangled KNN] task 2, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:14:25,494 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:14:25,647 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:14:28,529 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:14:43,799 [trainer.py] => No NME accuracy.
2025-07-17 13:14:43,800 [trainer.py] => CNN: {'total': 87.42, '00-99': 89.88, '100-109': 83.11, '110-119': 66.9, 'old': 89.25, 'new': 66.9}
2025-07-17 13:14:43,800 [trainer.py] => CNN HM: [0.0, 86.578, 76.476]
2025-07-17 13:14:43,800 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42]
2025-07-17 13:14:43,800 [trainer.py] => Average Accuracy (CNN): 89.11 

2025-07-17 13:14:43,803 [trainer.py] => All params: 88778881
2025-07-17 13:14:43,804 [trainer.py] => Trainable params: 2980225
2025-07-17 13:14:43,827 [ranpac.py] => Learning on 120-130
2025-07-17 13:14:47,445 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:14:47,472 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:14:47,737 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:14:47,737 [ranpac.py] => [Disentangled KNN] task 3, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:14:47,738 [ranpac.py] => [Disentangled KNN] task 3, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:14:47,738 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:14:47,904 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:14:50,422 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:15:06,040 [trainer.py] => No NME accuracy.
2025-07-17 13:15:06,040 [trainer.py] => CNN: {'total': 85.35, '00-99': 89.19, '100-109': 84.12, '110-119': 66.2, '120-129': 67.24, 'old': 86.87, 'new': 67.24}
2025-07-17 13:15:06,040 [trainer.py] => CNN HM: [0.0, 86.578, 76.476, 75.805]
2025-07-17 13:15:06,040 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42, 85.35]
2025-07-17 13:15:06,040 [trainer.py] => Average Accuracy (CNN): 88.16999999999999 

2025-07-17 13:15:06,042 [trainer.py] => All params: 88878881
2025-07-17 13:15:06,043 [trainer.py] => Trainable params: 3080225
2025-07-17 13:15:06,063 [ranpac.py] => Learning on 130-140
2025-07-17 13:15:10,858 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:15:10,882 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:15:11,186 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:15:11,186 [ranpac.py] => [Disentangled KNN] task 4, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:15:11,187 [ranpac.py] => [Disentangled KNN] task 4, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:15:11,187 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:15:11,364 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:15:14,432 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:15:34,088 [trainer.py] => No NME accuracy.
2025-07-17 13:15:34,088 [trainer.py] => CNN: {'total': 84.35, '00-99': 88.73, '100-109': 83.45, '110-119': 66.55, '120-129': 63.79, '130-139': 79.66, 'old': 84.71, 'new': 79.66}
2025-07-17 13:15:34,088 [trainer.py] => CNN HM: [0.0, 86.578, 76.476, 75.805, 82.107]
2025-07-17 13:15:34,088 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42, 85.35, 84.35]
2025-07-17 13:15:34,089 [trainer.py] => Average Accuracy (CNN): 87.40599999999999 

2025-07-17 13:15:34,091 [trainer.py] => All params: 88978881
2025-07-17 13:15:34,092 [trainer.py] => Trainable params: 3180225
2025-07-17 13:15:34,108 [ranpac.py] => Learning on 140-150
2025-07-17 13:15:37,832 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:15:37,850 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:15:38,120 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:15:38,120 [ranpac.py] => [Disentangled KNN] task 5, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:15:38,121 [ranpac.py] => [Disentangled KNN] task 5, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:15:38,121 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:15:38,293 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:15:41,159 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:15:56,628 [trainer.py] => No NME accuracy.
2025-07-17 13:15:56,629 [trainer.py] => CNN: {'total': 82.95, '00-99': 88.42, '100-109': 83.78, '110-119': 65.85, '120-129': 64.48, '130-139': 79.66, '140-149': 65.47, 'old': 84.15, 'new': 65.47}
2025-07-17 13:15:56,629 [trainer.py] => CNN HM: [0.0, 86.578, 76.476, 75.805, 82.107, 73.644]
2025-07-17 13:15:56,629 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42, 85.35, 84.35, 82.95]
2025-07-17 13:15:56,630 [trainer.py] => Average Accuracy (CNN): 86.66333333333334 

2025-07-17 13:15:56,632 [trainer.py] => All params: 89078881
2025-07-17 13:15:56,633 [trainer.py] => Trainable params: 3280225
2025-07-17 13:15:56,648 [ranpac.py] => Learning on 150-160
2025-07-17 13:16:00,159 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:16:00,200 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:16:00,448 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:16:00,449 [ranpac.py] => [Disentangled KNN] task 6, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:16:00,449 [ranpac.py] => [Disentangled KNN] task 6, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:16:00,450 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:16:00,640 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:16:03,050 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:16:19,724 [trainer.py] => No NME accuracy.
2025-07-17 13:16:19,725 [trainer.py] => CNN: {'total': 82.62, '00-99': 88.53, '100-109': 82.77, '110-119': 65.49, '120-129': 64.48, '130-139': 80.0, '140-149': 64.75, '150-159': 78.42, 'old': 82.91, 'new': 78.42}
2025-07-17 13:16:19,725 [trainer.py] => CNN HM: [0.0, 86.578, 76.476, 75.805, 82.107, 73.644, 80.603]
2025-07-17 13:16:19,725 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42, 85.35, 84.35, 82.95, 82.62]
2025-07-17 13:16:19,725 [trainer.py] => Average Accuracy (CNN): 86.08571428571429 

2025-07-17 13:16:19,726 [trainer.py] => All params: 89178881
2025-07-17 13:16:19,728 [trainer.py] => Trainable params: 3380225
2025-07-17 13:16:19,750 [ranpac.py] => Learning on 160-170
2025-07-17 13:16:22,465 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:16:22,489 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:16:22,763 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:16:22,763 [ranpac.py] => [Disentangled KNN] task 7, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:16:22,764 [ranpac.py] => [Disentangled KNN] task 7, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:16:22,764 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:16:23,004 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:16:26,790 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:16:48,579 [trainer.py] => No NME accuracy.
2025-07-17 13:16:48,580 [trainer.py] => CNN: {'total': 81.5, '00-99': 88.11, '100-109': 83.45, '110-119': 64.44, '120-129': 64.83, '130-139': 78.28, '140-149': 59.71, '150-159': 77.74, '160-169': 75.17, 'old': 81.91, 'new': 75.17}
2025-07-17 13:16:48,580 [trainer.py] => CNN HM: [0.0, 86.578, 76.476, 75.805, 82.107, 73.644, 80.603, 78.395]
2025-07-17 13:16:48,581 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42, 85.35, 84.35, 82.95, 82.62, 81.5]
2025-07-17 13:16:48,582 [trainer.py] => Average Accuracy (CNN): 85.5125 

2025-07-17 13:16:48,584 [trainer.py] => All params: 89278881
2025-07-17 13:16:48,586 [trainer.py] => Trainable params: 3480225
2025-07-17 13:16:48,625 [ranpac.py] => Learning on 170-180
2025-07-17 13:16:51,881 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:16:52,114 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:16:52,395 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:16:52,396 [ranpac.py] => [Disentangled KNN] task 8, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:16:52,397 [ranpac.py] => [Disentangled KNN] task 8, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:16:52,398 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:16:52,917 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:16:57,310 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:17:22,227 [trainer.py] => No NME accuracy.
2025-07-17 13:17:22,228 [trainer.py] => CNN: {'total': 80.85, '00-99': 87.73, '100-109': 83.45, '110-119': 64.44, '120-129': 64.83, '130-139': 76.55, '140-149': 58.63, '150-159': 77.4, '160-169': 73.49, '170-179': 78.52, 'old': 80.99, 'new': 78.52}
2025-07-17 13:17:22,228 [trainer.py] => CNN HM: [0.0, 86.578, 76.476, 75.805, 82.107, 73.644, 80.603, 78.395, 79.736]
2025-07-17 13:17:22,228 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42, 85.35, 84.35, 82.95, 82.62, 81.5, 80.85]
2025-07-17 13:17:22,228 [trainer.py] => Average Accuracy (CNN): 84.99444444444445 

2025-07-17 13:17:22,229 [trainer.py] => All params: 89378881
2025-07-17 13:17:22,231 [trainer.py] => Trainable params: 3580225
2025-07-17 13:17:22,257 [ranpac.py] => Learning on 180-190
2025-07-17 13:17:27,596 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:17:27,670 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:17:27,937 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:17:27,938 [ranpac.py] => [Disentangled KNN] task 9, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:17:27,938 [ranpac.py] => [Disentangled KNN] task 9, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:17:27,939 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:17:28,092 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:17:31,720 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:17:58,395 [trainer.py] => No NME accuracy.
2025-07-17 13:17:58,395 [trainer.py] => CNN: {'total': 80.36, '00-99': 87.66, '100-109': 84.46, '110-119': 64.08, '120-129': 64.48, '130-139': 75.17, '140-149': 59.35, '150-159': 78.08, '160-169': 73.15, '170-179': 78.86, '180-189': 71.78, 'old': 80.83, 'new': 71.78}
2025-07-17 13:17:58,396 [trainer.py] => CNN HM: [0.0, 86.578, 76.476, 75.805, 82.107, 73.644, 80.603, 78.395, 79.736, 76.037]
2025-07-17 13:17:58,398 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42, 85.35, 84.35, 82.95, 82.62, 81.5, 80.85, 80.36]
2025-07-17 13:17:58,398 [trainer.py] => Average Accuracy (CNN): 84.531 

2025-07-17 13:17:58,400 [trainer.py] => All params: 89478881
2025-07-17 13:17:58,401 [trainer.py] => Trainable params: 3680225
2025-07-17 13:17:58,427 [ranpac.py] => Learning on 190-200
2025-07-17 13:18:01,896 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:18:01,976 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:18:02,259 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:18:02,259 [ranpac.py] => [Disentangled KNN] task 10, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:18:02,260 [ranpac.py] => [Disentangled KNN] task 10, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:18:02,260 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:18:02,441 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:18:05,987 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:18:36,672 [trainer.py] => No NME accuracy.
2025-07-17 13:18:36,673 [trainer.py] => CNN: {'total': 79.29, '00-99': 87.18, '100-109': 84.46, '110-119': 64.08, '120-129': 64.83, '130-139': 75.17, '140-149': 59.35, '150-159': 77.74, '160-169': 72.82, '170-179': 78.19, '180-189': 71.43, '190-199': 65.54, 'old': 80.03, 'new': 65.54}
2025-07-17 13:18:36,673 [trainer.py] => CNN HM: [0.0, 86.578, 76.476, 75.805, 82.107, 73.644, 80.603, 78.395, 79.736, 76.037, 72.064]
2025-07-17 13:18:36,673 [trainer.py] => CNN top1 curve: [90.57, 89.34, 87.42, 85.35, 84.35, 82.95, 82.62, 81.5, 80.85, 80.36, 79.29]
2025-07-17 13:18:36,673 [trainer.py] => Average Accuracy (CNN): 84.05454545454546 

2025-07-17 13:18:36,675 [trainer.py] => Forgetting (CNN): 2.3619999999999997
2025-07-17 14:45:27,452 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-17 14:45:27,453 [trainer.py] => prefix: disentangled_reproduce
2025-07-17 14:45:27,453 [trainer.py] => dataset: cub
2025-07-17 14:45:27,453 [trainer.py] => memory_size: 0
2025-07-17 14:45:27,453 [trainer.py] => shuffle: True
2025-07-17 14:45:27,454 [trainer.py] => init_cls: 100
2025-07-17 14:45:27,454 [trainer.py] => increment: 10
2025-07-17 14:45:27,454 [trainer.py] => model_name: ranpac
2025-07-17 14:45:27,454 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-17 14:45:27,454 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-17 14:45:27,454 [trainer.py] => seed: 1993
2025-07-17 14:45:27,454 [trainer.py] => resume: False
2025-07-17 14:45:27,455 [trainer.py] => shot: 5
2025-07-17 14:45:27,455 [trainer.py] => use_simplecil: False
2025-07-17 14:45:27,455 [trainer.py] => tuned_epoch: 1
2025-07-17 14:45:27,455 [trainer.py] => init_lr: 0.01
2025-07-17 14:45:27,455 [trainer.py] => batch_size: 48
2025-07-17 14:45:27,455 [trainer.py] => weight_decay: 0.0005
2025-07-17 14:45:27,455 [trainer.py] => min_lr: 0
2025-07-17 14:45:27,455 [trainer.py] => ffn_num: 64
2025-07-17 14:45:27,456 [trainer.py] => optimizer: sgd
2025-07-17 14:45:27,456 [trainer.py] => use_RP: True
2025-07-17 14:45:27,456 [trainer.py] => M: 10000
2025-07-17 14:45:27,456 [trainer.py] => fecam: False
2025-07-17 14:45:27,456 [trainer.py] => calibration: True
2025-07-17 14:45:27,456 [trainer.py] => knn_k: 5
2025-07-17 14:45:27,456 [trainer.py] => knn_distance_metric: cosine
2025-07-17 14:45:27,456 [trainer.py] => knn_weight_decay: 0.1
2025-07-17 14:45:27,457 [trainer.py] => knn_adaptive_k: True
2025-07-17 14:45:27,457 [trainer.py] => knn_temperature: 16.0
2025-07-17 14:45:27,457 [trainer.py] => k_min: 3
2025-07-17 14:45:27,457 [trainer.py] => k_max: 21
2025-07-17 14:45:27,457 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-17 14:45:27,457 [trainer.py] => cosine_temperature: 16.0
2025-07-17 14:45:27,457 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-17 14:45:27,457 [trainer.py] => use_disentangled_adapter: True
2025-07-17 14:45:27,458 [trainer.py] => identity_bottleneck: 64
2025-07-17 14:45:27,458 [trainer.py] => variation_bottleneck: 64
2025-07-17 14:45:27,458 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-17 14:45:27,458 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-17 14:45:27,458 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-17 14:45:27,458 [trainer.py] => decorrelation_weight: 0.1
2025-07-17 14:45:27,458 [trainer.py] => orthogonal_weight: 0.01
2025-07-17 14:45:27,458 [trainer.py] => mutual_info_weight: 0.05
2025-07-17 14:45:27,459 [trainer.py] => contrastive_weight: 0.03
2025-07-17 14:45:27,459 [trainer.py] => disentangle_temperature: 0.1
2025-07-17 14:45:27,459 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-17 14:45:27,459 [trainer.py] => orthogonal_constraint: True
2025-07-17 14:45:27,459 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-17 14:45:27,459 [trainer.py] => variation_cov_weight: 0.8
2025-07-17 14:45:27,459 [trainer.py] => identity_similarity_weight: 1.0
2025-07-17 14:45:27,459 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-17 14:45:27,460 [trainer.py] => min_samples_for_cov: 5
2025-07-17 14:45:27,460 [trainer.py] => identity_similarity_metric: cosine
2025-07-17 14:45:27,460 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-17 14:45:27,460 [trainer.py] => identity_similarity_top_k: 5
2025-07-17 14:45:27,614 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-17 14:46:00,242 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-17 14:46:00,243 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-17 14:46:00,243 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-17 14:46:00,243 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-17 14:46:00,244 [trainer.py] => All params: 87578880
2025-07-17 14:46:00,245 [trainer.py] => Trainable params: 1780224
2025-07-17 14:46:02,379 [ranpac.py] => Learning on 0-100
2025-07-17 14:47:12,018 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.04, Test_accy 8.49
2025-07-17 14:47:31,930 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-17 14:47:53,783 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-17 14:47:54,466 [ranpac.py] => [Dynamic-K] Base similarity range: [7.0716, 10.8336]
2025-07-17 14:47:54,466 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-17 14:47:54,467 [trainer.py] => No NME accuracy.
2025-07-17 14:47:54,467 [trainer.py] => CNN: {'total': 90.57, '00-99': 90.57, 'old': 0, 'new': 90.57}
2025-07-17 14:47:54,468 [trainer.py] => CNN HM: [0.0]
2025-07-17 14:47:54,468 [trainer.py] => CNN top1 curve: [90.57]
2025-07-17 14:47:54,469 [trainer.py] => Average Accuracy (CNN): 90.57 

2025-07-17 14:47:54,473 [trainer.py] => All params: 88578881
2025-07-17 14:47:54,474 [trainer.py] => Trainable params: 1780225
2025-07-17 14:47:54,491 [ranpac.py] => Learning on 100-110
2025-07-17 14:47:57,773 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:47:57,793 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:47:58,072 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:47:58,072 [ranpac.py] => [Disentangled KNN] task 1, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:47:58,073 [ranpac.py] => [Disentangled KNN] task 1, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:47:58,073 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:48:01,893 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:48:23,379 [trainer.py] => No NME accuracy.
2025-07-17 14:48:23,380 [trainer.py] => CNN: {'total': 89.06, '00-99': 89.74, '100-109': 82.43, 'old': 89.74, 'new': 82.43}
2025-07-17 14:48:23,380 [trainer.py] => CNN HM: [0.0, 85.93]
2025-07-17 14:48:23,380 [trainer.py] => CNN top1 curve: [90.57, 89.06]
2025-07-17 14:48:23,380 [trainer.py] => Average Accuracy (CNN): 89.815 

2025-07-17 14:48:23,382 [trainer.py] => All params: 88678881
2025-07-17 14:48:23,384 [trainer.py] => Trainable params: 2880225
2025-07-17 14:48:23,403 [ranpac.py] => Learning on 110-120
2025-07-17 14:48:26,865 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:48:26,893 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:48:27,115 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:48:27,115 [ranpac.py] => [Disentangled KNN] task 2, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:48:27,116 [ranpac.py] => [Disentangled KNN] task 2, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:48:27,116 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:48:30,820 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:48:50,489 [trainer.py] => No NME accuracy.
2025-07-17 14:48:50,489 [trainer.py] => CNN: {'total': 87.42, '00-99': 90.02, '100-109': 83.45, '110-119': 65.14, 'old': 89.41, 'new': 65.14}
2025-07-17 14:48:50,489 [trainer.py] => CNN HM: [0.0, 85.93, 75.369]
2025-07-17 14:48:50,490 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42]
2025-07-17 14:48:50,490 [trainer.py] => Average Accuracy (CNN): 89.01666666666667 

2025-07-17 14:48:50,492 [trainer.py] => All params: 88778881
2025-07-17 14:48:50,494 [trainer.py] => Trainable params: 2980225
2025-07-17 14:48:50,513 [ranpac.py] => Learning on 120-130
2025-07-17 14:48:54,789 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:48:54,825 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:48:55,092 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:48:55,092 [ranpac.py] => [Disentangled KNN] task 3, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:48:55,093 [ranpac.py] => [Disentangled KNN] task 3, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:48:55,093 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:48:58,137 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:49:16,350 [trainer.py] => No NME accuracy.
2025-07-17 14:49:16,351 [trainer.py] => CNN: {'total': 85.35, '00-99': 89.25, '100-109': 83.45, '110-119': 65.85, '120-129': 67.59, 'old': 86.84, 'new': 67.59}
2025-07-17 14:49:16,351 [trainer.py] => CNN HM: [0.0, 85.93, 75.369, 76.015]
2025-07-17 14:49:16,351 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42, 85.35]
2025-07-17 14:49:16,351 [trainer.py] => Average Accuracy (CNN): 88.1 

2025-07-17 14:49:16,352 [trainer.py] => All params: 88878881
2025-07-17 14:49:16,354 [trainer.py] => Trainable params: 3080225
2025-07-17 14:49:16,377 [ranpac.py] => Learning on 130-140
2025-07-17 14:49:20,092 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:49:20,128 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:49:20,357 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:49:20,358 [ranpac.py] => [Disentangled KNN] task 4, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:49:20,359 [ranpac.py] => [Disentangled KNN] task 4, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:49:20,360 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:49:24,024 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:49:44,645 [trainer.py] => No NME accuracy.
2025-07-17 14:49:44,646 [trainer.py] => CNN: {'total': 84.35, '00-99': 88.8, '100-109': 83.11, '110-119': 66.55, '120-129': 64.48, '130-139': 78.62, 'old': 84.79, 'new': 78.62}
2025-07-17 14:49:44,647 [trainer.py] => CNN HM: [0.0, 85.93, 75.369, 76.015, 81.589]
2025-07-17 14:49:44,647 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42, 85.35, 84.35]
2025-07-17 14:49:44,648 [trainer.py] => Average Accuracy (CNN): 87.35 

2025-07-17 14:49:44,650 [trainer.py] => All params: 88978881
2025-07-17 14:49:44,651 [trainer.py] => Trainable params: 3180225
2025-07-17 14:49:44,672 [ranpac.py] => Learning on 140-150
2025-07-17 14:49:47,603 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:49:47,642 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:49:47,930 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:49:47,930 [ranpac.py] => [Disentangled KNN] task 5, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:49:47,932 [ranpac.py] => [Disentangled KNN] task 5, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:49:47,932 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:49:51,475 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:50:19,574 [trainer.py] => No NME accuracy.
2025-07-17 14:50:19,575 [trainer.py] => CNN: {'total': 83.02, '00-99': 88.63, '100-109': 84.12, '110-119': 64.79, '120-129': 64.83, '130-139': 78.97, '140-149': 65.47, 'old': 84.23, 'new': 65.47}
2025-07-17 14:50:19,575 [trainer.py] => CNN HM: [0.0, 85.93, 75.369, 76.015, 81.589, 73.675]
2025-07-17 14:50:19,576 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42, 85.35, 84.35, 83.02]
2025-07-17 14:50:19,576 [trainer.py] => Average Accuracy (CNN): 86.62833333333333 

2025-07-17 14:50:19,578 [trainer.py] => All params: 89078881
2025-07-17 14:50:19,579 [trainer.py] => Trainable params: 3280225
2025-07-17 14:50:19,602 [ranpac.py] => Learning on 150-160
2025-07-17 14:50:23,836 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:50:23,866 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:50:24,137 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:50:24,138 [ranpac.py] => [Disentangled KNN] task 6, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:50:24,140 [ranpac.py] => [Disentangled KNN] task 6, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:50:24,140 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:50:27,839 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:50:55,933 [trainer.py] => No NME accuracy.
2025-07-17 14:50:55,933 [trainer.py] => CNN: {'total': 82.64, '00-99': 88.56, '100-109': 82.77, '110-119': 65.14, '120-129': 65.52, '130-139': 79.31, '140-149': 64.75, '150-159': 78.42, 'old': 82.93, 'new': 78.42}
2025-07-17 14:50:55,933 [trainer.py] => CNN HM: [0.0, 85.93, 75.369, 76.015, 81.589, 73.675, 80.612]
2025-07-17 14:50:55,933 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42, 85.35, 84.35, 83.02, 82.64]
2025-07-17 14:50:55,933 [trainer.py] => Average Accuracy (CNN): 86.05857142857143 

2025-07-17 14:50:55,935 [trainer.py] => All params: 89178881
2025-07-17 14:50:55,936 [trainer.py] => Trainable params: 3380225
2025-07-17 14:50:55,959 [ranpac.py] => Learning on 160-170
2025-07-17 14:50:59,553 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:50:59,580 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:50:59,656 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:50:59,656 [ranpac.py] => [Disentangled KNN] task 7, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:50:59,657 [ranpac.py] => [Disentangled KNN] task 7, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:50:59,657 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:51:02,990 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:51:29,724 [trainer.py] => No NME accuracy.
2025-07-17 14:51:29,725 [trainer.py] => CNN: {'total': 81.46, '00-99': 88.11, '100-109': 82.77, '110-119': 63.73, '120-129': 65.86, '130-139': 78.28, '140-149': 58.99, '150-159': 77.74, '160-169': 75.5, 'old': 81.84, 'new': 75.5}
2025-07-17 14:51:29,725 [trainer.py] => CNN HM: [0.0, 85.93, 75.369, 76.015, 81.589, 73.675, 80.612, 78.542]
2025-07-17 14:51:29,725 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42, 85.35, 84.35, 83.02, 82.64, 81.46]
2025-07-17 14:51:29,725 [trainer.py] => Average Accuracy (CNN): 85.48375 

2025-07-17 14:51:29,726 [trainer.py] => All params: 89278881
2025-07-17 14:51:29,728 [trainer.py] => Trainable params: 3480225
2025-07-17 14:51:29,752 [ranpac.py] => Learning on 170-180
2025-07-17 14:51:32,566 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:51:32,609 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:51:32,877 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:51:32,877 [ranpac.py] => [Disentangled KNN] task 8, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:51:32,878 [ranpac.py] => [Disentangled KNN] task 8, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:51:32,878 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:51:35,997 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:52:14,689 [trainer.py] => No NME accuracy.
2025-07-17 14:52:14,690 [trainer.py] => CNN: {'total': 81.02, '00-99': 87.9, '100-109': 82.43, '110-119': 63.73, '120-129': 65.86, '130-139': 77.59, '140-149': 58.27, '150-159': 77.74, '160-169': 74.5, '170-179': 78.52, 'old': 81.17, 'new': 78.52}
2025-07-17 14:52:14,690 [trainer.py] => CNN HM: [0.0, 85.93, 75.369, 76.015, 81.589, 73.675, 80.612, 78.542, 79.823]
2025-07-17 14:52:14,690 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42, 85.35, 84.35, 83.02, 82.64, 81.46, 81.02]
2025-07-17 14:52:14,690 [trainer.py] => Average Accuracy (CNN): 84.98777777777778 

2025-07-17 14:52:14,692 [trainer.py] => All params: 89378881
2025-07-17 14:52:14,693 [trainer.py] => Trainable params: 3580225
2025-07-17 14:52:14,716 [ranpac.py] => Learning on 180-190
2025-07-17 14:52:18,734 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:52:18,768 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:52:19,029 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:52:19,030 [ranpac.py] => [Disentangled KNN] task 9, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:52:19,031 [ranpac.py] => [Disentangled KNN] task 9, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:52:19,032 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:52:22,598 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:52:53,231 [trainer.py] => No NME accuracy.
2025-07-17 14:52:53,232 [trainer.py] => CNN: {'total': 80.23, '00-99': 87.8, '100-109': 82.77, '110-119': 63.38, '120-129': 65.17, '130-139': 74.83, '140-149': 58.99, '150-159': 77.4, '160-169': 73.83, '170-179': 77.85, '180-189': 71.43, 'old': 80.71, 'new': 71.43}
2025-07-17 14:52:53,232 [trainer.py] => CNN HM: [0.0, 85.93, 75.369, 76.015, 81.589, 73.675, 80.612, 78.542, 79.823, 75.787]
2025-07-17 14:52:53,233 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42, 85.35, 84.35, 83.02, 82.64, 81.46, 81.02, 80.23]
2025-07-17 14:52:53,233 [trainer.py] => Average Accuracy (CNN): 84.512 

2025-07-17 14:52:53,235 [trainer.py] => All params: 89478881
2025-07-17 14:52:53,236 [trainer.py] => Trainable params: 3680225
2025-07-17 14:52:53,262 [ranpac.py] => Learning on 190-200
2025-07-17 14:52:57,673 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 14:52:57,702 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 14:52:57,978 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:52:57,979 [ranpac.py] => [Disentangled KNN] task 10, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 14:52:57,980 [ranpac.py] => [Disentangled KNN] task 10, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 14:52:57,981 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 14:53:01,351 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 14:53:41,811 [trainer.py] => No NME accuracy.
2025-07-17 14:53:41,812 [trainer.py] => CNN: {'total': 79.38, '00-99': 87.52, '100-109': 83.11, '110-119': 64.08, '120-129': 64.83, '130-139': 74.83, '140-149': 58.63, '150-159': 77.4, '160-169': 74.16, '170-179': 76.85, '180-189': 71.43, '190-199': 66.55, 'old': 80.07, 'new': 66.55}
2025-07-17 14:53:41,813 [trainer.py] => CNN HM: [0.0, 85.93, 75.369, 76.015, 81.589, 73.675, 80.612, 78.542, 79.823, 75.787, 72.687]
2025-07-17 14:53:41,813 [trainer.py] => CNN top1 curve: [90.57, 89.06, 87.42, 85.35, 84.35, 83.02, 82.64, 81.46, 81.02, 80.23, 79.38]
2025-07-17 14:53:41,813 [trainer.py] => Average Accuracy (CNN): 84.04545454545455 

2025-07-17 14:53:41,815 [trainer.py] => Forgetting (CNN): 2.464000000000001
2025-07-17 15:09:30,416 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-17 15:09:30,417 [trainer.py] => prefix: disentangled_reproduce
2025-07-17 15:09:30,417 [trainer.py] => dataset: cub
2025-07-17 15:09:30,417 [trainer.py] => memory_size: 0
2025-07-17 15:09:30,417 [trainer.py] => shuffle: True
2025-07-17 15:09:30,417 [trainer.py] => init_cls: 100
2025-07-17 15:09:30,417 [trainer.py] => increment: 10
2025-07-17 15:09:30,417 [trainer.py] => model_name: ranpac
2025-07-17 15:09:30,417 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-17 15:09:30,417 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-17 15:09:30,418 [trainer.py] => seed: 1993
2025-07-17 15:09:30,418 [trainer.py] => resume: False
2025-07-17 15:09:30,418 [trainer.py] => shot: 5
2025-07-17 15:09:30,418 [trainer.py] => use_simplecil: False
2025-07-17 15:09:30,418 [trainer.py] => tuned_epoch: 1
2025-07-17 15:09:30,418 [trainer.py] => init_lr: 0.01
2025-07-17 15:09:30,418 [trainer.py] => batch_size: 48
2025-07-17 15:09:30,418 [trainer.py] => weight_decay: 0.0005
2025-07-17 15:09:30,418 [trainer.py] => min_lr: 0
2025-07-17 15:09:30,418 [trainer.py] => ffn_num: 64
2025-07-17 15:09:30,419 [trainer.py] => optimizer: sgd
2025-07-17 15:09:30,419 [trainer.py] => use_RP: True
2025-07-17 15:09:30,419 [trainer.py] => M: 10000
2025-07-17 15:09:30,419 [trainer.py] => fecam: False
2025-07-17 15:09:30,419 [trainer.py] => calibration: True
2025-07-17 15:09:30,419 [trainer.py] => knn_k: 5
2025-07-17 15:09:30,419 [trainer.py] => knn_distance_metric: cosine
2025-07-17 15:09:30,419 [trainer.py] => knn_weight_decay: 0.1
2025-07-17 15:09:30,419 [trainer.py] => knn_adaptive_k: True
2025-07-17 15:09:30,419 [trainer.py] => knn_temperature: 16.0
2025-07-17 15:09:30,420 [trainer.py] => k_min: 3
2025-07-17 15:09:30,420 [trainer.py] => k_max: 21
2025-07-17 15:09:30,420 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-17 15:09:30,420 [trainer.py] => cosine_temperature: 16.0
2025-07-17 15:09:30,420 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-17 15:09:30,420 [trainer.py] => use_disentangled_adapter: True
2025-07-17 15:09:30,420 [trainer.py] => identity_bottleneck: 64
2025-07-17 15:09:30,420 [trainer.py] => variation_bottleneck: 64
2025-07-17 15:09:30,420 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-17 15:09:30,420 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-17 15:09:30,421 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-17 15:09:30,421 [trainer.py] => decorrelation_weight: 0.1
2025-07-17 15:09:30,421 [trainer.py] => orthogonal_weight: 0.01
2025-07-17 15:09:30,421 [trainer.py] => mutual_info_weight: 0.05
2025-07-17 15:09:30,421 [trainer.py] => contrastive_weight: 0.03
2025-07-17 15:09:30,421 [trainer.py] => disentangle_temperature: 0.1
2025-07-17 15:09:30,421 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-17 15:09:30,421 [trainer.py] => orthogonal_constraint: True
2025-07-17 15:09:30,421 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-17 15:09:30,421 [trainer.py] => variation_cov_weight: 0.8
2025-07-17 15:09:30,421 [trainer.py] => identity_similarity_weight: 1.0
2025-07-17 15:09:30,422 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-17 15:09:30,422 [trainer.py] => min_samples_for_cov: 5
2025-07-17 15:09:30,422 [trainer.py] => identity_similarity_metric: cosine
2025-07-17 15:09:30,422 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-17 15:09:30,422 [trainer.py] => identity_similarity_top_k: 5
2025-07-17 15:09:30,827 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-17 15:09:54,812 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-17 15:09:54,813 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-17 15:09:54,813 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-17 15:09:54,813 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-17 15:09:54,814 [trainer.py] => All params: 87578880
2025-07-17 15:09:54,815 [trainer.py] => Trainable params: 1780224
