2025-07-16 21:39:07,296 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/emergency_fix.json
2025-07-16 21:39:07,298 [trainer.py] => prefix: emergency_fix
2025-07-16 21:39:07,298 [trainer.py] => dataset: cub
2025-07-16 21:39:07,298 [trainer.py] => memory_size: 0
2025-07-16 21:39:07,298 [trainer.py] => shuffle: True
2025-07-16 21:39:07,299 [trainer.py] => init_cls: 100
2025-07-16 21:39:07,299 [trainer.py] => increment: 10
2025-07-16 21:39:07,299 [trainer.py] => model_name: ranpac
2025-07-16 21:39:07,299 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:39:07,299 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:39:07,299 [trainer.py] => seed: 1993
2025-07-16 21:39:07,299 [trainer.py] => resume: False
2025-07-16 21:39:07,299 [trainer.py] => shot: 5
2025-07-16 21:39:07,299 [trainer.py] => use_simplecil: False
2025-07-16 21:39:07,299 [trainer.py] => tuned_epoch: 40
2025-07-16 21:39:07,300 [trainer.py] => init_lr: 0.01
2025-07-16 21:39:07,300 [trainer.py] => batch_size: 48
2025-07-16 21:39:07,300 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:39:07,300 [trainer.py] => min_lr: 0
2025-07-16 21:39:07,300 [trainer.py] => ffn_num: 64
2025-07-16 21:39:07,300 [trainer.py] => optimizer: sgd
2025-07-16 21:39:07,300 [trainer.py] => use_RP: True
2025-07-16 21:39:07,300 [trainer.py] => M: 10000
2025-07-16 21:39:07,300 [trainer.py] => fecam: False
2025-07-16 21:39:07,300 [trainer.py] => calibration: True
2025-07-16 21:39:07,300 [trainer.py] => knn_k: 5
2025-07-16 21:39:07,300 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:39:07,301 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:39:07,301 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:39:07,301 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:39:07,301 [trainer.py] => k_min: 3
2025-07-16 21:39:07,301 [trainer.py] => k_max: 21
2025-07-16 21:39:07,301 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:39:07,301 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:39:07,301 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 21:39:07,301 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:39:07,301 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 21:39:07,545 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.identity_branch.0.weight', 'blocks.0.adaptmlp.identity_branch.0.bias', 'blocks.0.adaptmlp.variation_branch.0.weight', 'blocks.0.adaptmlp.variation_branch.0.bias', 'blocks.0.adaptmlp.identity_output_proj.weight', 'blocks.0.adaptmlp.identity_output_proj.bias', 'blocks.0.adaptmlp.variation_output_proj.weight', 'blocks.0.adaptmlp.variation_output_proj.bias', 'blocks.1.adaptmlp.identity_branch.0.weight', 'blocks.1.adaptmlp.identity_branch.0.bias', 'blocks.1.adaptmlp.variation_branch.0.weight', 'blocks.1.adaptmlp.variation_branch.0.bias', 'blocks.1.adaptmlp.identity_output_proj.weight', 'blocks.1.adaptmlp.identity_output_proj.bias', 'blocks.1.adaptmlp.variation_output_proj.weight', 'blocks.1.adaptmlp.variation_output_proj.bias', 'blocks.2.adaptmlp.identity_branch.0.weight', 'blocks.2.adaptmlp.identity_branch.0.bias', 'blocks.2.adaptmlp.variation_branch.0.weight', 'blocks.2.adaptmlp.variation_branch.0.bias', 'blocks.2.adaptmlp.identity_output_proj.weight', 'blocks.2.adaptmlp.identity_output_proj.bias', 'blocks.2.adaptmlp.variation_output_proj.weight', 'blocks.2.adaptmlp.variation_output_proj.bias', 'blocks.3.adaptmlp.identity_branch.0.weight', 'blocks.3.adaptmlp.identity_branch.0.bias', 'blocks.3.adaptmlp.variation_branch.0.weight', 'blocks.3.adaptmlp.variation_branch.0.bias', 'blocks.3.adaptmlp.identity_output_proj.weight', 'blocks.3.adaptmlp.identity_output_proj.bias', 'blocks.3.adaptmlp.variation_output_proj.weight', 'blocks.3.adaptmlp.variation_output_proj.bias', 'blocks.4.adaptmlp.identity_branch.0.weight', 'blocks.4.adaptmlp.identity_branch.0.bias', 'blocks.4.adaptmlp.variation_branch.0.weight', 'blocks.4.adaptmlp.variation_branch.0.bias', 'blocks.4.adaptmlp.identity_output_proj.weight', 'blocks.4.adaptmlp.identity_output_proj.bias', 'blocks.4.adaptmlp.variation_output_proj.weight', 'blocks.4.adaptmlp.variation_output_proj.bias', 'blocks.5.adaptmlp.identity_branch.0.weight', 'blocks.5.adaptmlp.identity_branch.0.bias', 'blocks.5.adaptmlp.variation_branch.0.weight', 'blocks.5.adaptmlp.variation_branch.0.bias', 'blocks.5.adaptmlp.identity_output_proj.weight', 'blocks.5.adaptmlp.identity_output_proj.bias', 'blocks.5.adaptmlp.variation_output_proj.weight', 'blocks.5.adaptmlp.variation_output_proj.bias', 'blocks.6.adaptmlp.identity_branch.0.weight', 'blocks.6.adaptmlp.identity_branch.0.bias', 'blocks.6.adaptmlp.variation_branch.0.weight', 'blocks.6.adaptmlp.variation_branch.0.bias', 'blocks.6.adaptmlp.identity_output_proj.weight', 'blocks.6.adaptmlp.identity_output_proj.bias', 'blocks.6.adaptmlp.variation_output_proj.weight', 'blocks.6.adaptmlp.variation_output_proj.bias', 'blocks.7.adaptmlp.identity_branch.0.weight', 'blocks.7.adaptmlp.identity_branch.0.bias', 'blocks.7.adaptmlp.variation_branch.0.weight', 'blocks.7.adaptmlp.variation_branch.0.bias', 'blocks.7.adaptmlp.identity_output_proj.weight', 'blocks.7.adaptmlp.identity_output_proj.bias', 'blocks.7.adaptmlp.variation_output_proj.weight', 'blocks.7.adaptmlp.variation_output_proj.bias', 'blocks.8.adaptmlp.identity_branch.0.weight', 'blocks.8.adaptmlp.identity_branch.0.bias', 'blocks.8.adaptmlp.variation_branch.0.weight', 'blocks.8.adaptmlp.variation_branch.0.bias', 'blocks.8.adaptmlp.identity_output_proj.weight', 'blocks.8.adaptmlp.identity_output_proj.bias', 'blocks.8.adaptmlp.variation_output_proj.weight', 'blocks.8.adaptmlp.variation_output_proj.bias', 'blocks.9.adaptmlp.identity_branch.0.weight', 'blocks.9.adaptmlp.identity_branch.0.bias', 'blocks.9.adaptmlp.variation_branch.0.weight', 'blocks.9.adaptmlp.variation_branch.0.bias', 'blocks.9.adaptmlp.identity_output_proj.weight', 'blocks.9.adaptmlp.identity_output_proj.bias', 'blocks.9.adaptmlp.variation_output_proj.weight', 'blocks.9.adaptmlp.variation_output_proj.bias', 'blocks.10.adaptmlp.identity_branch.0.weight', 'blocks.10.adaptmlp.identity_branch.0.bias', 'blocks.10.adaptmlp.variation_branch.0.weight', 'blocks.10.adaptmlp.variation_branch.0.bias', 'blocks.10.adaptmlp.identity_output_proj.weight', 'blocks.10.adaptmlp.identity_output_proj.bias', 'blocks.10.adaptmlp.variation_output_proj.weight', 'blocks.10.adaptmlp.variation_output_proj.bias', 'blocks.11.adaptmlp.identity_branch.0.weight', 'blocks.11.adaptmlp.identity_branch.0.bias', 'blocks.11.adaptmlp.variation_branch.0.weight', 'blocks.11.adaptmlp.variation_branch.0.bias', 'blocks.11.adaptmlp.identity_output_proj.weight', 'blocks.11.adaptmlp.identity_output_proj.bias', 'blocks.11.adaptmlp.variation_output_proj.weight', 'blocks.11.adaptmlp.variation_output_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-16 21:39:43,391 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:39:43,393 [trainer.py] => All params: 87578880
2025-07-16 21:39:43,393 [trainer.py] => Trainable params: 1780224
2025-07-16 21:39:54,540 [ranpac.py] => Learning on 0-100
87,655,681 total parameters.
1,857,025 training parameters.
backbone.blocks.0.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.0.adaptmlp.identity_branch.0.bias 64
backbone.blocks.0.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.0.adaptmlp.variation_branch.0.bias 64
backbone.blocks.0.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.0.adaptmlp.identity_output_proj.bias 384
backbone.blocks.0.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.0.adaptmlp.variation_output_proj.bias 384
backbone.blocks.1.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.1.adaptmlp.identity_branch.0.bias 64
backbone.blocks.1.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.1.adaptmlp.variation_branch.0.bias 64
backbone.blocks.1.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.1.adaptmlp.identity_output_proj.bias 384
backbone.blocks.1.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.1.adaptmlp.variation_output_proj.bias 384
backbone.blocks.2.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.2.adaptmlp.identity_branch.0.bias 64
backbone.blocks.2.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.2.adaptmlp.variation_branch.0.bias 64
backbone.blocks.2.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.2.adaptmlp.identity_output_proj.bias 384
backbone.blocks.2.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.2.adaptmlp.variation_output_proj.bias 384
backbone.blocks.3.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.3.adaptmlp.identity_branch.0.bias 64
backbone.blocks.3.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.3.adaptmlp.variation_branch.0.bias 64
backbone.blocks.3.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.3.adaptmlp.identity_output_proj.bias 384
backbone.blocks.3.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.3.adaptmlp.variation_output_proj.bias 384
backbone.blocks.4.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.4.adaptmlp.identity_branch.0.bias 64
backbone.blocks.4.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.4.adaptmlp.variation_branch.0.bias 64
backbone.blocks.4.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.4.adaptmlp.identity_output_proj.bias 384
backbone.blocks.4.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.4.adaptmlp.variation_output_proj.bias 384
backbone.blocks.5.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.5.adaptmlp.identity_branch.0.bias 64
backbone.blocks.5.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.5.adaptmlp.variation_branch.0.bias 64
backbone.blocks.5.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.5.adaptmlp.identity_output_proj.bias 384
backbone.blocks.5.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.5.adaptmlp.variation_output_proj.bias 384
backbone.blocks.6.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.6.adaptmlp.identity_branch.0.bias 64
backbone.blocks.6.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.6.adaptmlp.variation_branch.0.bias 64
backbone.blocks.6.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.6.adaptmlp.identity_output_proj.bias 384
backbone.blocks.6.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.6.adaptmlp.variation_output_proj.bias 384
backbone.blocks.7.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.7.adaptmlp.identity_branch.0.bias 64
backbone.blocks.7.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.7.adaptmlp.variation_branch.0.bias 64
backbone.blocks.7.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.7.adaptmlp.identity_output_proj.bias 384
backbone.blocks.7.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.7.adaptmlp.variation_output_proj.bias 384
backbone.blocks.8.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.8.adaptmlp.identity_branch.0.bias 64
backbone.blocks.8.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.8.adaptmlp.variation_branch.0.bias 64
backbone.blocks.8.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.8.adaptmlp.identity_output_proj.bias 384
backbone.blocks.8.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.8.adaptmlp.variation_output_proj.bias 384
backbone.blocks.9.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.9.adaptmlp.identity_branch.0.bias 64
backbone.blocks.9.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.9.adaptmlp.variation_branch.0.bias 64
backbone.blocks.9.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.9.adaptmlp.identity_output_proj.bias 384
backbone.blocks.9.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.9.adaptmlp.variation_output_proj.bias 384
backbone.blocks.10.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.10.adaptmlp.identity_branch.0.bias 64
backbone.blocks.10.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.10.adaptmlp.variation_branch.0.bias 64
backbone.blocks.10.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.10.adaptmlp.identity_output_proj.bias 384
backbone.blocks.10.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.10.adaptmlp.variation_output_proj.bias 384
backbone.blocks.11.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.11.adaptmlp.identity_branch.0.bias 64
backbone.blocks.11.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.11.adaptmlp.variation_branch.0.bias 64
backbone.blocks.11.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.11.adaptmlp.identity_output_proj.bias 384
backbone.blocks.11.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.11.adaptmlp.variation_output_proj.bias 384
fc.weight 76800
fc.sigma 1

  0%|          | 0/40 [00:00<?, ?it/s]
Task 0, Epoch 1/40 => Loss 16.591, Disentangle_Loss 120.000, Train_accy 3.17, Test_accy 8.56:   0%|          | 0/40 [01:38<?, ?it/s]
Task 0, Epoch 1/40 => Loss 16.591, Disentangle_Loss 120.000, Train_accy 3.17, Test_accy 8.56:   2%|▎         | 1/40 [01:38<1:03:46, 98.11s/it]
Task 0, Epoch 2/40 => Loss 17.804, Disentangle_Loss 132.745, Train_accy 19.29, Test_accy 41.21:   2%|▎         | 1/40 [02:31<1:03:46, 98.11s/it]
Task 0, Epoch 2/40 => Loss 17.804, Disentangle_Loss 132.745, Train_accy 19.29, Test_accy 41.21:   5%|▌         | 2/40 [02:31<45:28, 71.79s/it]  
Task 0, Epoch 3/40 => Loss 18.918, Disentangle_Loss 145.361, Train_accy 47.15, Test_accy 65.13:   5%|▌         | 2/40 [03:17<45:28, 71.79s/it]
Task 0, Epoch 3/40 => Loss 18.918, Disentangle_Loss 145.361, Train_accy 47.15, Test_accy 65.13:   8%|▊         | 3/40 [03:17<37:06, 60.19s/it]
Task 0, Epoch 4/40 => Loss 19.777, Disentangle_Loss 157.822, Train_accy 64.70, Test_accy 76.95:   8%|▊         | 3/40 [04:03<37:06, 60.19s/it]
Task 0, Epoch 4/40 => Loss 19.777, Disentangle_Loss 157.822, Train_accy 64.70, Test_accy 76.95:  10%|█         | 4/40 [04:03<32:41, 54.48s/it]
Task 0, Epoch 5/40 => Loss 20.180, Disentangle_Loss 170.468, Train_accy 73.41, Test_accy 82.95:  10%|█         | 4/40 [04:52<32:41, 54.48s/it]
Task 0, Epoch 5/40 => Loss 20.180, Disentangle_Loss 170.468, Train_accy 73.41, Test_accy 82.95:  12%|█▎        | 5/40 [04:52<30:41, 52.62s/it]
Task 0, Epoch 6/40 => Loss 20.303, Disentangle_Loss 183.087, Train_accy 78.45, Test_accy 86.86:  12%|█▎        | 5/40 [05:43<30:41, 52.62s/it]
Task 0, Epoch 6/40 => Loss 20.303, Disentangle_Loss 183.087, Train_accy 78.45, Test_accy 86.86:  15%|█▌        | 6/40 [05:43<29:27, 51.98s/it]
Task 0, Epoch 7/40 => Loss 20.835, Disentangle_Loss 195.700, Train_accy 82.68, Test_accy 88.11:  15%|█▌        | 6/40 [06:35<29:27, 51.98s/it]
Task 0, Epoch 7/40 => Loss 20.835, Disentangle_Loss 195.700, Train_accy 82.68, Test_accy 88.11:  18%|█▊        | 7/40 [06:35<28:30, 51.85s/it]
Task 0, Epoch 8/40 => Loss 21.826, Disentangle_Loss 208.314, Train_accy 84.08, Test_accy 89.50:  18%|█▊        | 7/40 [07:20<28:30, 51.85s/it]
Task 0, Epoch 8/40 => Loss 21.826, Disentangle_Loss 208.314, Train_accy 84.08, Test_accy 89.50:  20%|██        | 8/40 [07:20<26:28, 49.63s/it]
Task 0, Epoch 9/40 => Loss 22.931, Disentangle_Loss 220.957, Train_accy 84.85, Test_accy 90.02:  20%|██        | 8/40 [08:05<26:28, 49.63s/it]
Task 0, Epoch 9/40 => Loss 22.931, Disentangle_Loss 220.957, Train_accy 84.85, Test_accy 90.02:  22%|██▎       | 9/40 [08:05<24:59, 48.36s/it]
Task 0, Epoch 10/40 => Loss 24.104, Disentangle_Loss 233.528, Train_accy 86.22, Test_accy 90.02:  22%|██▎       | 9/40 [08:48<24:59, 48.36s/it]
Task 0, Epoch 10/40 => Loss 24.104, Disentangle_Loss 233.528, Train_accy 86.22, Test_accy 90.02:  25%|██▌       | 10/40 [08:48<23:17, 46.57s/it]
Task 0, Epoch 11/40 => Loss 25.330, Disentangle_Loss 246.102, Train_accy 85.92, Test_accy 90.78:  25%|██▌       | 10/40 [09:35<23:17, 46.57s/it]
Task 0, Epoch 11/40 => Loss 25.330, Disentangle_Loss 246.102, Train_accy 85.92, Test_accy 90.78:  28%|██▊       | 11/40 [09:35<22:40, 46.91s/it]
Task 0, Epoch 12/40 => Loss 25.060, Disentangle_Loss 244.009, Train_accy 86.92, Test_accy 90.99:  28%|██▊       | 11/40 [10:20<22:40, 46.91s/it]
Task 0, Epoch 12/40 => Loss 25.060, Disentangle_Loss 244.009, Train_accy 86.92, Test_accy 90.99:  30%|███       | 12/40 [10:20<21:37, 46.34s/it]
Task 0, Epoch 13/40 => Loss 24.775, Disentangle_Loss 241.896, Train_accy 88.62, Test_accy 91.06:  30%|███       | 12/40 [11:06<21:37, 46.34s/it]
Task 0, Epoch 13/40 => Loss 24.775, Disentangle_Loss 241.896, Train_accy 88.62, Test_accy 91.06:  32%|███▎      | 13/40 [11:06<20:41, 45.97s/it]
Task 0, Epoch 14/40 => Loss 24.530, Disentangle_Loss 239.765, Train_accy 89.26, Test_accy 91.51:  32%|███▎      | 13/40 [11:55<20:41, 45.97s/it]
Task 0, Epoch 14/40 => Loss 24.530, Disentangle_Loss 239.765, Train_accy 89.26, Test_accy 91.51:  35%|███▌      | 14/40 [11:55<20:19, 46.90s/it]
Task 0, Epoch 15/40 => Loss 24.331, Disentangle_Loss 237.691, Train_accy 88.59, Test_accy 91.30:  35%|███▌      | 14/40 [12:38<20:19, 46.90s/it]
Task 0, Epoch 15/40 => Loss 24.331, Disentangle_Loss 237.691, Train_accy 88.59, Test_accy 91.30:  38%|███▊      | 15/40 [12:38<19:04, 45.77s/it]
Task 0, Epoch 16/40 => Loss 24.088, Disentangle_Loss 235.516, Train_accy 89.72, Test_accy 91.58:  38%|███▊      | 15/40 [13:19<19:04, 45.77s/it]
Task 0, Epoch 16/40 => Loss 24.088, Disentangle_Loss 235.516, Train_accy 89.72, Test_accy 91.58:  40%|████      | 16/40 [13:19<17:47, 44.48s/it]
Task 0, Epoch 17/40 => Loss 23.864, Disentangle_Loss 233.404, Train_accy 89.46, Test_accy 91.89:  40%|████      | 16/40 [14:05<17:47, 44.48s/it]
Task 0, Epoch 17/40 => Loss 23.864, Disentangle_Loss 233.404, Train_accy 89.46, Test_accy 91.89:  42%|████▎     | 17/40 [14:05<17:08, 44.71s/it]
Task 0, Epoch 18/40 => Loss 23.643, Disentangle_Loss 231.214, Train_accy 90.02, Test_accy 91.92:  42%|████▎     | 17/40 [14:50<17:08, 44.71s/it]
Task 0, Epoch 18/40 => Loss 23.643, Disentangle_Loss 231.214, Train_accy 90.02, Test_accy 91.92:  45%|████▌     | 18/40 [14:50<16:28, 44.94s/it]
Task 0, Epoch 19/40 => Loss 23.381, Disentangle_Loss 228.926, Train_accy 90.16, Test_accy 91.99:  45%|████▌     | 18/40 [15:38<16:28, 44.94s/it]
Task 0, Epoch 19/40 => Loss 23.381, Disentangle_Loss 228.926, Train_accy 90.16, Test_accy 91.99:  48%|████▊     | 19/40 [15:38<16:00, 45.73s/it]
Task 0, Epoch 20/40 => Loss 23.170, Disentangle_Loss 226.534, Train_accy 89.49, Test_accy 91.96:  48%|████▊     | 19/40 [16:23<16:00, 45.73s/it]
Task 0, Epoch 20/40 => Loss 23.170, Disentangle_Loss 226.534, Train_accy 89.49, Test_accy 91.96:  50%|█████     | 20/40 [16:23<15:14, 45.72s/it]
Task 0, Epoch 21/40 => Loss 22.925, Disentangle_Loss 224.364, Train_accy 90.49, Test_accy 92.17:  50%|█████     | 20/40 [17:08<15:14, 45.72s/it]
Task 0, Epoch 21/40 => Loss 22.925, Disentangle_Loss 224.364, Train_accy 90.49, Test_accy 92.17:  52%|█████▎    | 21/40 [17:08<14:23, 45.47s/it]
Task 0, Epoch 22/40 => Loss 22.703, Disentangle_Loss 222.268, Train_accy 90.79, Test_accy 92.03:  52%|█████▎    | 21/40 [17:53<14:23, 45.47s/it]
Task 0, Epoch 22/40 => Loss 22.703, Disentangle_Loss 222.268, Train_accy 90.79, Test_accy 92.03:  55%|█████▌    | 22/40 [17:53<13:32, 45.16s/it]
Task 0, Epoch 23/40 => Loss 22.495, Disentangle_Loss 220.185, Train_accy 89.99, Test_accy 92.10:  55%|█████▌    | 22/40 [18:44<13:32, 45.16s/it]
Task 0, Epoch 23/40 => Loss 22.495, Disentangle_Loss 220.185, Train_accy 89.99, Test_accy 92.10:  57%|█████▊    | 23/40 [18:44<13:21, 47.17s/it]
Task 0, Epoch 24/40 => Loss 22.254, Disentangle_Loss 218.086, Train_accy 91.49, Test_accy 92.10:  57%|█████▊    | 23/40 [19:30<13:21, 47.17s/it]
Task 0, Epoch 24/40 => Loss 22.254, Disentangle_Loss 218.086, Train_accy 91.49, Test_accy 92.10:  60%|██████    | 24/40 [19:30<12:24, 46.55s/it]
Task 0, Epoch 25/40 => Loss 22.049, Disentangle_Loss 215.996, Train_accy 90.82, Test_accy 92.24:  60%|██████    | 24/40 [20:18<12:24, 46.55s/it]
Task 0, Epoch 25/40 => Loss 22.049, Disentangle_Loss 215.996, Train_accy 90.82, Test_accy 92.24:  62%|██████▎   | 25/40 [20:18<11:48, 47.26s/it]
Task 0, Epoch 26/40 => Loss 21.856, Disentangle_Loss 213.908, Train_accy 90.56, Test_accy 92.31:  62%|██████▎   | 25/40 [21:11<11:48, 47.26s/it]
Task 0, Epoch 26/40 => Loss 21.856, Disentangle_Loss 213.908, Train_accy 90.56, Test_accy 92.31:  65%|██████▌   | 26/40 [21:11<11:22, 48.75s/it]
Task 0, Epoch 27/40 => Loss 21.640, Disentangle_Loss 211.819, Train_accy 90.52, Test_accy 92.13:  65%|██████▌   | 26/40 [21:56<11:22, 48.75s/it]
Task 0, Epoch 27/40 => Loss 21.640, Disentangle_Loss 211.819, Train_accy 90.52, Test_accy 92.13:  68%|██████▊   | 27/40 [21:56<10:22, 47.85s/it]
Task 0, Epoch 28/40 => Loss 21.382, Disentangle_Loss 209.736, Train_accy 92.73, Test_accy 92.03:  68%|██████▊   | 27/40 [22:48<10:22, 47.85s/it]
Task 0, Epoch 28/40 => Loss 21.382, Disentangle_Loss 209.736, Train_accy 92.73, Test_accy 92.03:  70%|███████   | 28/40 [22:48<09:48, 49.05s/it]
Task 0, Epoch 29/40 => Loss 21.189, Disentangle_Loss 207.645, Train_accy 91.62, Test_accy 92.17:  70%|███████   | 28/40 [23:34<09:48, 49.05s/it]
Task 0, Epoch 29/40 => Loss 21.189, Disentangle_Loss 207.645, Train_accy 91.62, Test_accy 92.17:  72%|███████▎  | 29/40 [23:34<08:49, 48.11s/it]
Task 0, Epoch 30/40 => Loss 20.991, Disentangle_Loss 205.557, Train_accy 91.56, Test_accy 92.24:  72%|███████▎  | 29/40 [24:18<08:49, 48.11s/it]
Task 0, Epoch 30/40 => Loss 20.991, Disentangle_Loss 205.557, Train_accy 91.56, Test_accy 92.24:  75%|███████▌  | 30/40 [24:18<07:49, 46.91s/it]
Task 0, Epoch 31/40 => Loss 20.777, Disentangle_Loss 203.469, Train_accy 91.62, Test_accy 92.51:  75%|███████▌  | 30/40 [25:08<07:49, 46.91s/it]
Task 0, Epoch 31/40 => Loss 20.777, Disentangle_Loss 203.469, Train_accy 91.62, Test_accy 92.51:  78%|███████▊  | 31/40 [25:08<07:10, 47.87s/it]
Task 0, Epoch 32/40 => Loss 20.576, Disentangle_Loss 201.381, Train_accy 90.82, Test_accy 92.44:  78%|███████▊  | 31/40 [25:54<07:10, 47.87s/it]
Task 0, Epoch 32/40 => Loss 20.576, Disentangle_Loss 201.381, Train_accy 90.82, Test_accy 92.44:  80%|████████  | 32/40 [25:54<06:18, 47.28s/it]
Task 0, Epoch 33/40 => Loss 20.344, Disentangle_Loss 199.295, Train_accy 91.39, Test_accy 92.48:  80%|████████  | 32/40 [26:48<06:18, 47.28s/it]
Task 0, Epoch 33/40 => Loss 20.344, Disentangle_Loss 199.295, Train_accy 91.39, Test_accy 92.48:  82%|████████▎ | 33/40 [26:48<05:44, 49.21s/it]
Task 0, Epoch 34/40 => Loss 20.139, Disentangle_Loss 197.207, Train_accy 91.76, Test_accy 92.48:  82%|████████▎ | 33/40 [27:37<05:44, 49.21s/it]
Task 0, Epoch 34/40 => Loss 20.139, Disentangle_Loss 197.207, Train_accy 91.76, Test_accy 92.48:  85%|████████▌ | 34/40 [27:37<04:54, 49.01s/it]
Task 0, Epoch 35/40 => Loss 19.936, Disentangle_Loss 195.118, Train_accy 91.73, Test_accy 92.41:  85%|████████▌ | 34/40 [28:22<04:54, 49.01s/it]
Task 0, Epoch 35/40 => Loss 19.936, Disentangle_Loss 195.118, Train_accy 91.73, Test_accy 92.41:  88%|████████▊ | 35/40 [28:22<04:00, 48.04s/it]
Task 0, Epoch 36/40 => Loss 19.696, Disentangle_Loss 193.036, Train_accy 92.39, Test_accy 92.48:  88%|████████▊ | 35/40 [29:07<04:00, 48.04s/it]
Task 0, Epoch 36/40 => Loss 19.696, Disentangle_Loss 193.036, Train_accy 92.39, Test_accy 92.48:  90%|█████████ | 36/40 [29:07<03:07, 46.96s/it]
Task 0, Epoch 37/40 => Loss 19.506, Disentangle_Loss 190.949, Train_accy 92.16, Test_accy 92.51:  90%|█████████ | 36/40 [29:57<03:07, 46.96s/it]
Task 0, Epoch 37/40 => Loss 19.506, Disentangle_Loss 190.949, Train_accy 92.16, Test_accy 92.51:  92%|█████████▎| 37/40 [29:57<02:23, 47.80s/it]
Task 0, Epoch 38/40 => Loss 19.289, Disentangle_Loss 188.858, Train_accy 92.19, Test_accy 92.51:  92%|█████████▎| 37/40 [30:42<02:23, 47.80s/it]
Task 0, Epoch 38/40 => Loss 19.289, Disentangle_Loss 188.858, Train_accy 92.19, Test_accy 92.51:  95%|█████████▌| 38/40 [30:42<01:34, 47.13s/it]
Task 0, Epoch 39/40 => Loss 19.077, Disentangle_Loss 186.776, Train_accy 92.29, Test_accy 92.48:  95%|█████████▌| 38/40 [31:29<01:34, 47.13s/it]
Task 0, Epoch 39/40 => Loss 19.077, Disentangle_Loss 186.776, Train_accy 92.29, Test_accy 92.48:  98%|█████████▊| 39/40 [31:29<00:47, 47.07s/it]
Task 0, Epoch 40/40 => Loss 18.885, Disentangle_Loss 184.684, Train_accy 92.23, Test_accy 92.48:  98%|█████████▊| 39/40 [32:18<00:47, 47.07s/it]
Task 0, Epoch 40/40 => Loss 18.885, Disentangle_Loss 184.684, Train_accy 92.23, Test_accy 92.48: 100%|██████████| 40/40 [32:18<00:00, 47.76s/it]
Task 0, Epoch 40/40 => Loss 18.885, Disentangle_Loss 184.684, Train_accy 92.23, Test_accy 92.48: 100%|██████████| 40/40 [32:18<00:00, 48.47s/it]
2025-07-16 22:12:13,455 [ranpac.py] => Task 0, Epoch 40/40 => Loss 18.885, Disentangle_Loss 184.684, Train_accy 92.23, Test_accy 92.48
2025-07-16 22:12:36,919 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
selected lambda =  1000000.0
2025-07-16 22:13:09,905 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 22:13:10,347 [ranpac.py] => [Dynamic-K] Base similarity range: [0.8417, 2.1442]
2025-07-16 22:13:10,349 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 22:13:10,351 [trainer.py] => No NME accuracy.
2025-07-16 22:13:10,352 [trainer.py] => CNN: {'total': 92.31, '00-99': 92.31, 'old': 0, 'new': 92.31}
2025-07-16 22:13:10,354 [trainer.py] => CNN HM: [0.0]
2025-07-16 22:13:10,355 [trainer.py] => CNN top1 curve: [92.31]
Average Accuracy (CNN): 92.31
2025-07-16 22:13:10,356 [trainer.py] => Average Accuracy (CNN): 92.31 

2025-07-16 22:13:10,358 [trainer.py] => All params: 88578881
2025-07-16 22:13:10,360 [trainer.py] => Trainable params: 1780225
2025-07-16 22:13:10,523 [ranpac.py] => Learning on 100-110
2025-07-16 22:13:14,116 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:13:14,315 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:13:14,318 [ranpac.py] => [Dynamic-K] Computed K values: [17, 17, 11, 13, 6, 9, 12, 15, 13, 5]
2025-07-16 22:13:14,319 [ranpac.py] => [KNN] task 1, dynamic K values: [17, 17, 11, 13, 6, 9, 12, 15, 13, 5]
2025-07-16 22:13:17,260 [ranpac.py] => [KNN] task 1, weight sparsity: 0.882, distance_metric: cosine
2025-07-16 22:13:17,262 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
/home/<USER>/workdir/FSCIL-Calibration-main/utils/disentangled_covariance.py:271: RuntimeWarning: covariance is not positive-semidefinite.
  samples = np.random.multivariate_normal(
2025-07-16 22:13:20,547 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:13:46,816 [trainer.py] => No NME accuracy.
2025-07-16 22:13:46,819 [trainer.py] => CNN: {'total': 91.48, '00-99': 91.68, '100-109': 89.53, 'old': 91.68, 'new': 89.53}
2025-07-16 22:13:46,819 [trainer.py] => CNN HM: [0.0, 90.592]
2025-07-16 22:13:46,820 [trainer.py] => CNN top1 curve: [92.31, 91.48]
Average Accuracy (CNN): 91.89500000000001
2025-07-16 22:13:46,821 [trainer.py] => Average Accuracy (CNN): 91.89500000000001 

2025-07-16 22:13:46,823 [trainer.py] => All params: 88678881
2025-07-16 22:13:46,825 [trainer.py] => Trainable params: 2880225
2025-07-16 22:13:46,840 [ranpac.py] => Learning on 110-120
2025-07-16 22:13:50,315 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:13:50,334 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:13:50,336 [ranpac.py] => [Dynamic-K] Computed K values: [6, 14, 14, 9, 17, 14, 12, 7, 17, 13]
2025-07-16 22:13:50,336 [ranpac.py] => [KNN] task 2, dynamic K values: [6, 14, 14, 9, 17, 14, 12, 7, 17, 13]
2025-07-16 22:13:50,337 [ranpac.py] => [KNN] task 2, weight sparsity: 0.877, distance_metric: cosine
2025-07-16 22:13:50,337 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:13:52,084 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:14:18,648 [trainer.py] => No NME accuracy.
2025-07-16 22:14:18,650 [trainer.py] => CNN: {'total': 90.19, '00-99': 91.61, '100-109': 89.53, '110-119': 76.41, 'old': 91.42, 'new': 76.41}
2025-07-16 22:14:18,651 [trainer.py] => CNN HM: [0.0, 90.592, 83.244]
2025-07-16 22:14:18,651 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19]
Average Accuracy (CNN): 91.32666666666667
2025-07-16 22:14:18,652 [trainer.py] => Average Accuracy (CNN): 91.32666666666667 

2025-07-16 22:14:18,653 [trainer.py] => All params: 88778881
2025-07-16 22:14:18,654 [trainer.py] => Trainable params: 2980225
2025-07-16 22:14:18,674 [ranpac.py] => Learning on 120-130
2025-07-16 22:14:22,143 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:14:22,156 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:14:22,159 [ranpac.py] => [Dynamic-K] Computed K values: [12, 11, 14, 13, 10, 6, 4, 10, 7, 10]
2025-07-16 22:14:22,161 [ranpac.py] => [KNN] task 3, dynamic K values: [12, 11, 14, 13, 10, 6, 4, 10, 7, 10]
2025-07-16 22:14:22,163 [ranpac.py] => [KNN] task 3, weight sparsity: 0.903, distance_metric: cosine
2025-07-16 22:14:22,164 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:14:23,720 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:14:48,360 [trainer.py] => No NME accuracy.
2025-07-16 22:14:48,362 [trainer.py] => CNN: {'total': 88.84, '00-99': 91.33, '100-109': 90.88, '110-119': 76.06, '120-129': 74.48, 'old': 90.04, 'new': 74.48}
2025-07-16 22:14:48,362 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524]
2025-07-16 22:14:48,363 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84]
Average Accuracy (CNN): 90.70500000000001
2025-07-16 22:14:48,363 [trainer.py] => Average Accuracy (CNN): 90.70500000000001 

2025-07-16 22:14:48,365 [trainer.py] => All params: 88878881
2025-07-16 22:14:48,367 [trainer.py] => Trainable params: 3080225
2025-07-16 22:14:48,381 [ranpac.py] => Learning on 130-140
2025-07-16 22:14:52,251 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:14:52,267 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:14:52,270 [ranpac.py] => [Dynamic-K] Computed K values: [14, 11, 16, 11, 20, 5, 14, 11, 12, 12]
2025-07-16 22:14:52,271 [ranpac.py] => [KNN] task 4, dynamic K values: [14, 11, 16, 11, 20, 5, 14, 11, 12, 12]
2025-07-16 22:14:52,273 [ranpac.py] => [KNN] task 4, weight sparsity: 0.874, distance_metric: cosine
2025-07-16 22:14:52,274 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:14:54,027 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:15:19,779 [trainer.py] => No NME accuracy.
2025-07-16 22:15:19,781 [trainer.py] => CNN: {'total': 88.28, '00-99': 91.16, '100-109': 90.54, '110-119': 76.06, '120-129': 72.07, '130-139': 85.52, 'old': 88.5, 'new': 85.52}
2025-07-16 22:15:19,782 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984]
2025-07-16 22:15:19,783 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28]
Average Accuracy (CNN): 90.22
2025-07-16 22:15:19,783 [trainer.py] => Average Accuracy (CNN): 90.22 

2025-07-16 22:15:19,785 [trainer.py] => All params: 88978881
2025-07-16 22:15:19,786 [trainer.py] => Trainable params: 3180225
2025-07-16 22:15:19,803 [ranpac.py] => Learning on 140-150
2025-07-16 22:15:22,714 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:15:22,732 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:15:22,735 [ranpac.py] => [Dynamic-K] Computed K values: [9, 16, 13, 7, 12, 15, 16, 5, 6, 16]
2025-07-16 22:15:22,736 [ranpac.py] => [KNN] task 5, dynamic K values: [9, 16, 13, 7, 12, 15, 16, 5, 6, 16]
2025-07-16 22:15:22,737 [ranpac.py] => [KNN] task 5, weight sparsity: 0.885, distance_metric: cosine
2025-07-16 22:15:22,738 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:15:24,554 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:15:55,416 [trainer.py] => No NME accuracy.
2025-07-16 22:15:55,419 [trainer.py] => CNN: {'total': 86.63, '00-99': 90.54, '100-109': 90.88, '110-119': 73.24, '120-129': 70.69, '130-139': 85.86, '140-149': 72.66, 'old': 87.59, 'new': 72.66}
2025-07-16 22:15:55,419 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43]
2025-07-16 22:15:55,419 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63]
Average Accuracy (CNN): 89.62166666666667
2025-07-16 22:15:55,420 [trainer.py] => Average Accuracy (CNN): 89.62166666666667 

2025-07-16 22:15:55,421 [trainer.py] => All params: 89078881
2025-07-16 22:15:55,422 [trainer.py] => Trainable params: 3280225
2025-07-16 22:15:55,443 [ranpac.py] => Learning on 150-160
2025-07-16 22:15:59,183 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:15:59,199 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:15:59,202 [ranpac.py] => [Dynamic-K] Computed K values: [11, 15, 11, 8, 13, 8, 7, 8, 21, 13]
2025-07-16 22:15:59,204 [ranpac.py] => [KNN] task 6, dynamic K values: [11, 15, 11, 8, 13, 8, 7, 8, 21, 13]
2025-07-16 22:15:59,208 [ranpac.py] => [KNN] task 6, weight sparsity: 0.885, distance_metric: cosine
2025-07-16 22:15:59,209 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:16:00,798 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:16:35,097 [trainer.py] => No NME accuracy.
2025-07-16 22:16:35,099 [trainer.py] => CNN: {'total': 86.26, '00-99': 90.64, '100-109': 88.51, '110-119': 73.94, '120-129': 70.34, '130-139': 85.86, '140-149': 71.94, '150-159': 82.53, 'old': 86.51, 'new': 82.53}
2025-07-16 22:16:35,100 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473]
2025-07-16 22:16:35,101 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26]
Average Accuracy (CNN): 89.14142857142858
2025-07-16 22:16:35,101 [trainer.py] => Average Accuracy (CNN): 89.14142857142858 

2025-07-16 22:16:35,104 [trainer.py] => All params: 89178881
2025-07-16 22:16:35,105 [trainer.py] => Trainable params: 3380225
2025-07-16 22:16:35,123 [ranpac.py] => Learning on 160-170
2025-07-16 22:16:38,732 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:16:38,753 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:16:38,755 [ranpac.py] => [Dynamic-K] Computed K values: [15, 18, 16, 5, 16, 11, 10, 6, 12, 11]
2025-07-16 22:16:38,757 [ranpac.py] => [KNN] task 7, dynamic K values: [15, 18, 16, 5, 16, 11, 10, 6, 12, 11]
2025-07-16 22:16:38,758 [ranpac.py] => [KNN] task 7, weight sparsity: 0.880, distance_metric: cosine
2025-07-16 22:16:38,759 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:16:40,345 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:17:10,868 [trainer.py] => No NME accuracy.
2025-07-16 22:17:10,870 [trainer.py] => CNN: {'total': 85.77, '00-99': 90.4, '100-109': 88.51, '110-119': 73.24, '120-129': 71.03, '130-139': 85.52, '140-149': 67.63, '150-159': 83.56, '160-169': 83.89, 'old': 85.89, 'new': 83.89}
2025-07-16 22:17:10,870 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473, 84.878]
2025-07-16 22:17:10,871 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26, 85.77]
Average Accuracy (CNN): 88.72
2025-07-16 22:17:10,871 [trainer.py] => Average Accuracy (CNN): 88.72 

2025-07-16 22:17:10,872 [trainer.py] => All params: 89278881
2025-07-16 22:17:10,873 [trainer.py] => Trainable params: 3480225
2025-07-16 22:17:10,891 [ranpac.py] => Learning on 170-180
2025-07-16 22:17:13,081 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:17:13,129 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:17:13,132 [ranpac.py] => [Dynamic-K] Computed K values: [17, 7, 12, 10, 15, 7, 6, 9, 17, 8]
2025-07-16 22:17:13,133 [ranpac.py] => [KNN] task 8, dynamic K values: [17, 7, 12, 10, 15, 7, 6, 9, 17, 8]
2025-07-16 22:17:13,136 [ranpac.py] => [KNN] task 8, weight sparsity: 0.892, distance_metric: cosine
2025-07-16 22:17:13,138 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:17:14,875 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:17:54,772 [trainer.py] => No NME accuracy.
2025-07-16 22:17:54,774 [trainer.py] => CNN: {'total': 85.15, '00-99': 89.84, '100-109': 88.51, '110-119': 72.54, '120-129': 71.03, '130-139': 86.21, '140-149': 66.91, '150-159': 82.19, '160-169': 83.56, '170-179': 82.55, 'old': 85.3, 'new': 82.55}
2025-07-16 22:17:54,775 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473, 84.878, 83.902]
2025-07-16 22:17:54,775 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26, 85.77, 85.15]
Average Accuracy (CNN): 88.32333333333332
2025-07-16 22:17:54,776 [trainer.py] => Average Accuracy (CNN): 88.32333333333332 

2025-07-16 22:17:54,778 [trainer.py] => All params: 89378881
2025-07-16 22:17:54,779 [trainer.py] => Trainable params: 3580225
2025-07-16 22:17:54,796 [ranpac.py] => Learning on 180-190
2025-07-16 22:17:58,488 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:17:58,503 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:17:58,506 [ranpac.py] => [Dynamic-K] Computed K values: [12, 12, 8, 14, 11, 9, 14, 15, 18, 11]
2025-07-16 22:17:58,508 [ranpac.py] => [KNN] task 9, dynamic K values: [12, 12, 8, 14, 11, 9, 14, 15, 18, 11]
2025-07-16 22:17:58,510 [ranpac.py] => [KNN] task 9, weight sparsity: 0.876, distance_metric: cosine
2025-07-16 22:17:58,511 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:18:00,332 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:18:40,261 [trainer.py] => No NME accuracy.
2025-07-16 22:18:40,264 [trainer.py] => CNN: {'total': 84.56, '00-99': 89.77, '100-109': 89.19, '110-119': 72.18, '120-129': 70.34, '130-139': 83.79, '140-149': 66.55, '150-159': 82.53, '160-169': 83.22, '170-179': 81.88, '180-189': 78.4, 'old': 84.9, 'new': 78.4}
2025-07-16 22:18:40,264 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473, 84.878, 83.902, 81.521]
2025-07-16 22:18:40,264 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26, 85.77, 85.15, 84.56]
Average Accuracy (CNN): 87.947
2025-07-16 22:18:40,265 [trainer.py] => Average Accuracy (CNN): 87.947 

2025-07-16 22:18:40,267 [trainer.py] => All params: 89478881
2025-07-16 22:18:40,268 [trainer.py] => Trainable params: 3680225
2025-07-16 22:18:40,289 [ranpac.py] => Learning on 190-200
2025-07-16 22:18:45,924 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:18:45,942 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:18:45,944 [ranpac.py] => [Dynamic-K] Computed K values: [5, 14, 12, 10, 13, 7, 8, 3, 15, 8]
2025-07-16 22:18:45,945 [ranpac.py] => [KNN] task 10, dynamic K values: [5, 14, 12, 10, 13, 7, 8, 3, 15, 8]
2025-07-16 22:18:45,947 [ranpac.py] => [KNN] task 10, weight sparsity: 0.905, distance_metric: cosine
2025-07-16 22:18:45,948 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:18:47,795 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:19:27,417 [trainer.py] => No NME accuracy.
2025-07-16 22:19:27,419 [trainer.py] => CNN: {'total': 83.72, '00-99': 89.36, '100-109': 89.86, '110-119': 72.89, '120-129': 71.03, '130-139': 83.79, '140-149': 66.55, '150-159': 82.53, '160-169': 82.21, '170-179': 80.87, '180-189': 78.05, '190-199': 72.64, 'old': 84.32, 'new': 72.64}
2025-07-16 22:19:27,419 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473, 84.878, 83.902, 81.521, 78.045]
2025-07-16 22:19:27,419 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26, 85.77, 85.15, 84.56, 83.72]
Average Accuracy (CNN): 87.56272727272727
2025-07-16 22:19:27,420 [trainer.py] => Average Accuracy (CNN): 87.56272727272727 

Accuracy Matrix (CNN):
[[92.31 91.68 91.61 91.33 91.16 90.54 90.64 90.4  89.84 89.77 89.36]
 [ 0.   89.53 89.53 90.88 90.54 90.88 88.51 88.51 88.51 89.19 89.86]
 [ 0.    0.   76.41 76.06 76.06 73.24 73.94 73.24 72.54 72.18 72.89]
 [ 0.    0.    0.   74.48 72.07 70.69 70.34 71.03 71.03 70.34 71.03]
 [ 0.    0.    0.    0.   85.52 85.86 85.86 85.52 86.21 83.79 83.79]
 [ 0.    0.    0.    0.    0.   72.66 71.94 67.63 66.91 66.55 66.55]
 [ 0.    0.    0.    0.    0.    0.   82.53 83.56 82.19 82.53 82.53]
 [ 0.    0.    0.    0.    0.    0.    0.   83.89 83.56 83.22 82.21]
 [ 0.    0.    0.    0.    0.    0.    0.    0.   82.55 81.88 80.87]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.   78.4  78.05]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.    0.   72.64]]
2025-07-16 22:19:27,421 [trainer.py] => Forgetting (CNN): 2.4209999999999994
