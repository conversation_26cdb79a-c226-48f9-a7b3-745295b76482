2025-07-17 13:19:28,233 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/ranpac_disentangled.json
2025-07-17 13:19:28,235 [trainer.py] => prefix: disentangled_reproduce
2025-07-17 13:19:28,236 [trainer.py] => dataset: cub
2025-07-17 13:19:28,236 [trainer.py] => memory_size: 0
2025-07-17 13:19:28,236 [trainer.py] => shuffle: True
2025-07-17 13:19:28,236 [trainer.py] => init_cls: 100
2025-07-17 13:19:28,236 [trainer.py] => increment: 10
2025-07-17 13:19:28,236 [trainer.py] => model_name: ranpac
2025-07-17 13:19:28,236 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-17 13:19:28,237 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-17 13:19:28,237 [trainer.py] => seed: 1993
2025-07-17 13:19:28,237 [trainer.py] => resume: False
2025-07-17 13:19:28,237 [trainer.py] => shot: 5
2025-07-17 13:19:28,237 [trainer.py] => use_simplecil: False
2025-07-17 13:19:28,237 [trainer.py] => tuned_epoch: 1
2025-07-17 13:19:28,237 [trainer.py] => init_lr: 0.01
2025-07-17 13:19:28,237 [trainer.py] => batch_size: 48
2025-07-17 13:19:28,238 [trainer.py] => weight_decay: 0.0005
2025-07-17 13:19:28,238 [trainer.py] => min_lr: 0
2025-07-17 13:19:28,238 [trainer.py] => ffn_num: 64
2025-07-17 13:19:28,238 [trainer.py] => optimizer: sgd
2025-07-17 13:19:28,238 [trainer.py] => use_RP: True
2025-07-17 13:19:28,238 [trainer.py] => M: 10000
2025-07-17 13:19:28,238 [trainer.py] => fecam: False
2025-07-17 13:19:28,238 [trainer.py] => calibration: True
2025-07-17 13:19:28,239 [trainer.py] => knn_k: 5
2025-07-17 13:19:28,239 [trainer.py] => knn_distance_metric: cosine
2025-07-17 13:19:28,239 [trainer.py] => knn_weight_decay: 0.1
2025-07-17 13:19:28,239 [trainer.py] => knn_adaptive_k: True
2025-07-17 13:19:28,239 [trainer.py] => knn_temperature: 16.0
2025-07-17 13:19:28,239 [trainer.py] => k_min: 3
2025-07-17 13:19:28,239 [trainer.py] => k_max: 21
2025-07-17 13:19:28,240 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-17 13:19:28,240 [trainer.py] => cosine_temperature: 16.0
2025-07-17 13:19:28,240 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-17 13:19:28,240 [trainer.py] => use_disentangled_adapter: True
2025-07-17 13:19:28,240 [trainer.py] => identity_bottleneck: 64
2025-07-17 13:19:28,240 [trainer.py] => variation_bottleneck: 64
2025-07-17 13:19:28,240 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-17 13:19:28,240 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-17 13:19:28,241 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-17 13:19:28,241 [trainer.py] => decorrelation_weight: 0.1
2025-07-17 13:19:28,241 [trainer.py] => orthogonal_weight: 0.01
2025-07-17 13:19:28,241 [trainer.py] => mutual_info_weight: 0.05
2025-07-17 13:19:28,241 [trainer.py] => contrastive_weight: 0.03
2025-07-17 13:19:28,241 [trainer.py] => disentangle_temperature: 0.1
2025-07-17 13:19:28,241 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-17 13:19:28,241 [trainer.py] => orthogonal_constraint: True
2025-07-17 13:19:28,242 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-17 13:19:28,242 [trainer.py] => variation_cov_weight: 0.8
2025-07-17 13:19:28,242 [trainer.py] => identity_similarity_weight: 1.0
2025-07-17 13:19:28,242 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-17 13:19:28,242 [trainer.py] => min_samples_for_cov: 5
2025-07-17 13:19:28,242 [trainer.py] => identity_similarity_metric: cosine
2025-07-17 13:19:28,242 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-17 13:19:28,242 [trainer.py] => identity_similarity_top_k: 5
2025-07-17 13:19:28,427 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.identity_branch.0.weight', 'blocks.0.adaptmlp.identity_branch.0.bias', 'blocks.0.adaptmlp.variation_branch.0.weight', 'blocks.0.adaptmlp.variation_branch.0.bias', 'blocks.0.adaptmlp.identity_output_proj.weight', 'blocks.0.adaptmlp.identity_output_proj.bias', 'blocks.0.adaptmlp.variation_output_proj.weight', 'blocks.0.adaptmlp.variation_output_proj.bias', 'blocks.1.adaptmlp.identity_branch.0.weight', 'blocks.1.adaptmlp.identity_branch.0.bias', 'blocks.1.adaptmlp.variation_branch.0.weight', 'blocks.1.adaptmlp.variation_branch.0.bias', 'blocks.1.adaptmlp.identity_output_proj.weight', 'blocks.1.adaptmlp.identity_output_proj.bias', 'blocks.1.adaptmlp.variation_output_proj.weight', 'blocks.1.adaptmlp.variation_output_proj.bias', 'blocks.2.adaptmlp.identity_branch.0.weight', 'blocks.2.adaptmlp.identity_branch.0.bias', 'blocks.2.adaptmlp.variation_branch.0.weight', 'blocks.2.adaptmlp.variation_branch.0.bias', 'blocks.2.adaptmlp.identity_output_proj.weight', 'blocks.2.adaptmlp.identity_output_proj.bias', 'blocks.2.adaptmlp.variation_output_proj.weight', 'blocks.2.adaptmlp.variation_output_proj.bias', 'blocks.3.adaptmlp.identity_branch.0.weight', 'blocks.3.adaptmlp.identity_branch.0.bias', 'blocks.3.adaptmlp.variation_branch.0.weight', 'blocks.3.adaptmlp.variation_branch.0.bias', 'blocks.3.adaptmlp.identity_output_proj.weight', 'blocks.3.adaptmlp.identity_output_proj.bias', 'blocks.3.adaptmlp.variation_output_proj.weight', 'blocks.3.adaptmlp.variation_output_proj.bias', 'blocks.4.adaptmlp.identity_branch.0.weight', 'blocks.4.adaptmlp.identity_branch.0.bias', 'blocks.4.adaptmlp.variation_branch.0.weight', 'blocks.4.adaptmlp.variation_branch.0.bias', 'blocks.4.adaptmlp.identity_output_proj.weight', 'blocks.4.adaptmlp.identity_output_proj.bias', 'blocks.4.adaptmlp.variation_output_proj.weight', 'blocks.4.adaptmlp.variation_output_proj.bias', 'blocks.5.adaptmlp.identity_branch.0.weight', 'blocks.5.adaptmlp.identity_branch.0.bias', 'blocks.5.adaptmlp.variation_branch.0.weight', 'blocks.5.adaptmlp.variation_branch.0.bias', 'blocks.5.adaptmlp.identity_output_proj.weight', 'blocks.5.adaptmlp.identity_output_proj.bias', 'blocks.5.adaptmlp.variation_output_proj.weight', 'blocks.5.adaptmlp.variation_output_proj.bias', 'blocks.6.adaptmlp.identity_branch.0.weight', 'blocks.6.adaptmlp.identity_branch.0.bias', 'blocks.6.adaptmlp.variation_branch.0.weight', 'blocks.6.adaptmlp.variation_branch.0.bias', 'blocks.6.adaptmlp.identity_output_proj.weight', 'blocks.6.adaptmlp.identity_output_proj.bias', 'blocks.6.adaptmlp.variation_output_proj.weight', 'blocks.6.adaptmlp.variation_output_proj.bias', 'blocks.7.adaptmlp.identity_branch.0.weight', 'blocks.7.adaptmlp.identity_branch.0.bias', 'blocks.7.adaptmlp.variation_branch.0.weight', 'blocks.7.adaptmlp.variation_branch.0.bias', 'blocks.7.adaptmlp.identity_output_proj.weight', 'blocks.7.adaptmlp.identity_output_proj.bias', 'blocks.7.adaptmlp.variation_output_proj.weight', 'blocks.7.adaptmlp.variation_output_proj.bias', 'blocks.8.adaptmlp.identity_branch.0.weight', 'blocks.8.adaptmlp.identity_branch.0.bias', 'blocks.8.adaptmlp.variation_branch.0.weight', 'blocks.8.adaptmlp.variation_branch.0.bias', 'blocks.8.adaptmlp.identity_output_proj.weight', 'blocks.8.adaptmlp.identity_output_proj.bias', 'blocks.8.adaptmlp.variation_output_proj.weight', 'blocks.8.adaptmlp.variation_output_proj.bias', 'blocks.9.adaptmlp.identity_branch.0.weight', 'blocks.9.adaptmlp.identity_branch.0.bias', 'blocks.9.adaptmlp.variation_branch.0.weight', 'blocks.9.adaptmlp.variation_branch.0.bias', 'blocks.9.adaptmlp.identity_output_proj.weight', 'blocks.9.adaptmlp.identity_output_proj.bias', 'blocks.9.adaptmlp.variation_output_proj.weight', 'blocks.9.adaptmlp.variation_output_proj.bias', 'blocks.10.adaptmlp.identity_branch.0.weight', 'blocks.10.adaptmlp.identity_branch.0.bias', 'blocks.10.adaptmlp.variation_branch.0.weight', 'blocks.10.adaptmlp.variation_branch.0.bias', 'blocks.10.adaptmlp.identity_output_proj.weight', 'blocks.10.adaptmlp.identity_output_proj.bias', 'blocks.10.adaptmlp.variation_output_proj.weight', 'blocks.10.adaptmlp.variation_output_proj.bias', 'blocks.11.adaptmlp.identity_branch.0.weight', 'blocks.11.adaptmlp.identity_branch.0.bias', 'blocks.11.adaptmlp.variation_branch.0.weight', 'blocks.11.adaptmlp.variation_branch.0.bias', 'blocks.11.adaptmlp.identity_output_proj.weight', 'blocks.11.adaptmlp.identity_output_proj.bias', 'blocks.11.adaptmlp.variation_output_proj.weight', 'blocks.11.adaptmlp.variation_output_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-17 13:19:58,997 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-17 13:19:59,000 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-17 13:19:59,000 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-17 13:19:59,000 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-17 13:19:59,001 [trainer.py] => All params: 87578880
2025-07-17 13:19:59,001 [trainer.py] => Trainable params: 1780224
2025-07-17 13:20:25,905 [ranpac.py] => Learning on 0-100
87,655,681 total parameters.
1,857,025 training parameters.
backbone.blocks.0.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.0.adaptmlp.identity_branch.0.bias 64
backbone.blocks.0.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.0.adaptmlp.variation_branch.0.bias 64
backbone.blocks.0.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.0.adaptmlp.identity_output_proj.bias 384
backbone.blocks.0.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.0.adaptmlp.variation_output_proj.bias 384
backbone.blocks.1.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.1.adaptmlp.identity_branch.0.bias 64
backbone.blocks.1.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.1.adaptmlp.variation_branch.0.bias 64
backbone.blocks.1.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.1.adaptmlp.identity_output_proj.bias 384
backbone.blocks.1.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.1.adaptmlp.variation_output_proj.bias 384
backbone.blocks.2.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.2.adaptmlp.identity_branch.0.bias 64
backbone.blocks.2.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.2.adaptmlp.variation_branch.0.bias 64
backbone.blocks.2.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.2.adaptmlp.identity_output_proj.bias 384
backbone.blocks.2.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.2.adaptmlp.variation_output_proj.bias 384
backbone.blocks.3.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.3.adaptmlp.identity_branch.0.bias 64
backbone.blocks.3.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.3.adaptmlp.variation_branch.0.bias 64
backbone.blocks.3.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.3.adaptmlp.identity_output_proj.bias 384
backbone.blocks.3.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.3.adaptmlp.variation_output_proj.bias 384
backbone.blocks.4.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.4.adaptmlp.identity_branch.0.bias 64
backbone.blocks.4.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.4.adaptmlp.variation_branch.0.bias 64
backbone.blocks.4.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.4.adaptmlp.identity_output_proj.bias 384
backbone.blocks.4.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.4.adaptmlp.variation_output_proj.bias 384
backbone.blocks.5.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.5.adaptmlp.identity_branch.0.bias 64
backbone.blocks.5.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.5.adaptmlp.variation_branch.0.bias 64
backbone.blocks.5.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.5.adaptmlp.identity_output_proj.bias 384
backbone.blocks.5.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.5.adaptmlp.variation_output_proj.bias 384
backbone.blocks.6.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.6.adaptmlp.identity_branch.0.bias 64
backbone.blocks.6.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.6.adaptmlp.variation_branch.0.bias 64
backbone.blocks.6.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.6.adaptmlp.identity_output_proj.bias 384
backbone.blocks.6.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.6.adaptmlp.variation_output_proj.bias 384
backbone.blocks.7.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.7.adaptmlp.identity_branch.0.bias 64
backbone.blocks.7.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.7.adaptmlp.variation_branch.0.bias 64
backbone.blocks.7.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.7.adaptmlp.identity_output_proj.bias 384
backbone.blocks.7.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.7.adaptmlp.variation_output_proj.bias 384
backbone.blocks.8.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.8.adaptmlp.identity_branch.0.bias 64
backbone.blocks.8.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.8.adaptmlp.variation_branch.0.bias 64
backbone.blocks.8.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.8.adaptmlp.identity_output_proj.bias 384
backbone.blocks.8.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.8.adaptmlp.variation_output_proj.bias 384
backbone.blocks.9.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.9.adaptmlp.identity_branch.0.bias 64
backbone.blocks.9.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.9.adaptmlp.variation_branch.0.bias 64
backbone.blocks.9.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.9.adaptmlp.identity_output_proj.bias 384
backbone.blocks.9.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.9.adaptmlp.variation_output_proj.bias 384
backbone.blocks.10.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.10.adaptmlp.identity_branch.0.bias 64
backbone.blocks.10.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.10.adaptmlp.variation_branch.0.bias 64
backbone.blocks.10.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.10.adaptmlp.identity_output_proj.bias 384
backbone.blocks.10.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.10.adaptmlp.variation_output_proj.bias 384
backbone.blocks.11.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.11.adaptmlp.identity_branch.0.bias 64
backbone.blocks.11.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.11.adaptmlp.variation_branch.0.bias 64
backbone.blocks.11.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.11.adaptmlp.identity_output_proj.bias 384
backbone.blocks.11.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.11.adaptmlp.variation_output_proj.bias 384
fc.weight 76800
fc.sigma 1

  0%|          | 0/1 [00:00<?, ?it/s]
Task 0, Epoch 1/1 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.17, Test_accy 8.56:   0%|          | 0/1 [01:32<?, ?it/s]
Task 0, Epoch 1/1 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.17, Test_accy 8.56: 100%|██████████| 1/1 [01:32<00:00, 92.36s/it]
Task 0, Epoch 1/1 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.17, Test_accy 8.56: 100%|██████████| 1/1 [01:32<00:00, 92.36s/it]
2025-07-17 13:21:58,292 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.593, Disentangle_Loss 1.200, Train_accy 3.17, Test_accy 8.56
2025-07-17 13:22:19,706 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
selected lambda =  1000000.0
2025-07-17 13:22:49,681 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-17 13:22:50,130 [ranpac.py] => [Dynamic-K] Base similarity range: [7.0714, 10.8337]
2025-07-17 13:22:50,130 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-17 13:22:50,131 [trainer.py] => No NME accuracy.
2025-07-17 13:22:50,131 [trainer.py] => CNN: {'total': 90.61, '00-99': 90.61, 'old': 0, 'new': 90.61}
2025-07-17 13:22:50,131 [trainer.py] => CNN HM: [0.0]
2025-07-17 13:22:50,131 [trainer.py] => CNN top1 curve: [90.61]
Average Accuracy (CNN): 90.61
2025-07-17 13:22:50,131 [trainer.py] => Average Accuracy (CNN): 90.61 

2025-07-17 13:22:50,132 [trainer.py] => All params: 88578881
2025-07-17 13:22:50,132 [trainer.py] => Trainable params: 1780225
2025-07-17 13:22:50,141 [ranpac.py] => Learning on 100-110
2025-07-17 13:22:53,036 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:22:54,192 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:22:54,194 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:22:54,195 [ranpac.py] => [Disentangled KNN] task 1, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:22:54,662 [ranpac.py] => [Disentangled KNN] task 1, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:22:54,664 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:22:54,742 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
/home/<USER>/workdir/FSCIL-Calibration-main/utils/disentangled_covariance.py:271: RuntimeWarning: covariance is not positive-semidefinite.
  samples = np.random.multivariate_normal(
2025-07-17 13:22:57,934 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:23:20,764 [trainer.py] => No NME accuracy.
2025-07-17 13:23:20,766 [trainer.py] => CNN: {'total': 89.28, '00-99': 89.88, '100-109': 83.45, 'old': 89.88, 'new': 83.45}
2025-07-17 13:23:20,766 [trainer.py] => CNN HM: [0.0, 86.546]
2025-07-17 13:23:20,766 [trainer.py] => CNN top1 curve: [90.61, 89.28]
Average Accuracy (CNN): 89.945
2025-07-17 13:23:20,766 [trainer.py] => Average Accuracy (CNN): 89.945 

2025-07-17 13:23:20,767 [trainer.py] => All params: 88678881
2025-07-17 13:23:20,768 [trainer.py] => Trainable params: 2880225
2025-07-17 13:23:20,778 [ranpac.py] => Learning on 110-120
2025-07-17 13:23:23,138 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:23:23,152 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:23:23,155 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:23:23,156 [ranpac.py] => [Disentangled KNN] task 2, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:23:23,158 [ranpac.py] => [Disentangled KNN] task 2, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:23:23,159 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:23:23,229 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:23:24,844 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:23:47,430 [trainer.py] => No NME accuracy.
2025-07-17 13:23:47,432 [trainer.py] => CNN: {'total': 87.45, '00-99': 89.88, '100-109': 83.11, '110-119': 67.25, 'old': 89.25, 'new': 67.25}
2025-07-17 13:23:47,432 [trainer.py] => CNN HM: [0.0, 86.546, 76.704]
2025-07-17 13:23:47,432 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45]
Average Accuracy (CNN): 89.11333333333333
2025-07-17 13:23:47,433 [trainer.py] => Average Accuracy (CNN): 89.11333333333333 

2025-07-17 13:23:47,435 [trainer.py] => All params: 88778881
2025-07-17 13:23:47,436 [trainer.py] => Trainable params: 2980225
2025-07-17 13:23:47,449 [ranpac.py] => Learning on 120-130
2025-07-17 13:23:51,071 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:23:51,085 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:23:51,087 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:23:51,088 [ranpac.py] => [Disentangled KNN] task 3, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:23:51,090 [ranpac.py] => [Disentangled KNN] task 3, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:23:51,091 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:23:51,162 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:23:52,814 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:24:13,714 [trainer.py] => No NME accuracy.
2025-07-17 13:24:13,717 [trainer.py] => CNN: {'total': 85.25, '00-99': 89.12, '100-109': 84.12, '110-119': 66.2, '120-129': 66.55, 'old': 86.81, 'new': 66.55}
2025-07-17 13:24:13,717 [trainer.py] => CNN HM: [0.0, 86.546, 76.704, 75.342]
2025-07-17 13:24:13,717 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45, 85.25]
Average Accuracy (CNN): 88.1475
2025-07-17 13:24:13,717 [trainer.py] => Average Accuracy (CNN): 88.1475 

2025-07-17 13:24:13,718 [trainer.py] => All params: 88878881
2025-07-17 13:24:13,718 [trainer.py] => Trainable params: 3080225
2025-07-17 13:24:13,732 [ranpac.py] => Learning on 130-140
2025-07-17 13:24:17,327 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:24:17,341 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:24:17,342 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:24:17,342 [ranpac.py] => [Disentangled KNN] task 4, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:24:17,343 [ranpac.py] => [Disentangled KNN] task 4, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:24:17,343 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:24:17,411 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:24:19,142 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:24:45,738 [trainer.py] => No NME accuracy.
2025-07-17 13:24:45,740 [trainer.py] => CNN: {'total': 84.43, '00-99': 88.87, '100-109': 83.45, '110-119': 66.55, '120-129': 64.14, '130-139': 78.97, 'old': 84.85, 'new': 78.97}
2025-07-17 13:24:45,740 [trainer.py] => CNN HM: [0.0, 86.546, 76.704, 75.342, 81.804]
2025-07-17 13:24:45,740 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45, 85.25, 84.43]
Average Accuracy (CNN): 87.404
2025-07-17 13:24:45,741 [trainer.py] => Average Accuracy (CNN): 87.404 

2025-07-17 13:24:45,742 [trainer.py] => All params: 88978881
2025-07-17 13:24:45,743 [trainer.py] => Trainable params: 3180225
2025-07-17 13:24:45,759 [ranpac.py] => Learning on 140-150
2025-07-17 13:24:50,329 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:24:50,344 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:24:50,345 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:24:50,345 [ranpac.py] => [Disentangled KNN] task 5, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:24:50,345 [ranpac.py] => [Disentangled KNN] task 5, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:24:50,345 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:24:50,412 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:24:52,118 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:25:22,847 [trainer.py] => No NME accuracy.
2025-07-17 13:25:22,847 [trainer.py] => CNN: {'total': 83.09, '00-99': 88.53, '100-109': 84.46, '110-119': 65.85, '120-129': 64.83, '130-139': 79.66, '140-149': 65.47, 'old': 84.3, 'new': 65.47}
2025-07-17 13:25:22,847 [trainer.py] => CNN HM: [0.0, 86.546, 76.704, 75.342, 81.804, 73.701]
2025-07-17 13:25:22,847 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45, 85.25, 84.43, 83.09]
Average Accuracy (CNN): 86.685
2025-07-17 13:25:22,848 [trainer.py] => Average Accuracy (CNN): 86.685 

2025-07-17 13:25:22,850 [trainer.py] => All params: 89078881
2025-07-17 13:25:22,851 [trainer.py] => Trainable params: 3280225
2025-07-17 13:25:22,864 [ranpac.py] => Learning on 150-160
2025-07-17 13:25:27,218 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:25:27,234 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:25:27,235 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:25:27,235 [ranpac.py] => [Disentangled KNN] task 6, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:25:27,236 [ranpac.py] => [Disentangled KNN] task 6, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:25:27,236 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:25:27,313 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:25:29,057 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:25:57,665 [trainer.py] => No NME accuracy.
2025-07-17 13:25:57,666 [trainer.py] => CNN: {'total': 82.56, '00-99': 88.42, '100-109': 83.11, '110-119': 65.49, '120-129': 64.48, '130-139': 80.0, '140-149': 64.39, '150-159': 78.42, 'old': 82.84, 'new': 78.42}
2025-07-17 13:25:57,666 [trainer.py] => CNN HM: [0.0, 86.546, 76.704, 75.342, 81.804, 73.701, 80.569]
2025-07-17 13:25:57,666 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45, 85.25, 84.43, 83.09, 82.56]
Average Accuracy (CNN): 86.0957142857143
2025-07-17 13:25:57,666 [trainer.py] => Average Accuracy (CNN): 86.0957142857143 

2025-07-17 13:25:57,668 [trainer.py] => All params: 89178881
2025-07-17 13:25:57,669 [trainer.py] => Trainable params: 3380225
2025-07-17 13:25:57,684 [ranpac.py] => Learning on 160-170
2025-07-17 13:26:01,784 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:26:01,799 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:26:01,800 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:26:01,801 [ranpac.py] => [Disentangled KNN] task 7, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:26:01,801 [ranpac.py] => [Disentangled KNN] task 7, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:26:01,801 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:26:01,875 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:26:03,595 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:26:32,062 [trainer.py] => No NME accuracy.
2025-07-17 13:26:32,062 [trainer.py] => CNN: {'total': 81.54, '00-99': 88.15, '100-109': 84.12, '110-119': 64.08, '120-129': 64.83, '130-139': 78.28, '140-149': 60.07, '150-159': 77.4, '160-169': 75.17, 'old': 81.95, 'new': 75.17}
2025-07-17 13:26:32,062 [trainer.py] => CNN HM: [0.0, 86.546, 76.704, 75.342, 81.804, 73.701, 80.569, 78.414]
2025-07-17 13:26:32,062 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45, 85.25, 84.43, 83.09, 82.56, 81.54]
Average Accuracy (CNN): 85.52625
2025-07-17 13:26:32,062 [trainer.py] => Average Accuracy (CNN): 85.52625 

2025-07-17 13:26:32,063 [trainer.py] => All params: 89278881
2025-07-17 13:26:32,064 [trainer.py] => Trainable params: 3480225
2025-07-17 13:26:32,086 [ranpac.py] => Learning on 170-180
2025-07-17 13:26:34,460 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:26:34,472 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:26:34,473 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:26:34,474 [ranpac.py] => [Disentangled KNN] task 8, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:26:34,474 [ranpac.py] => [Disentangled KNN] task 8, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:26:34,474 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:26:34,542 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:26:36,289 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:27:05,670 [trainer.py] => No NME accuracy.
2025-07-17 13:27:05,670 [trainer.py] => CNN: {'total': 80.98, '00-99': 87.8, '100-109': 84.12, '110-119': 64.08, '120-129': 64.83, '130-139': 76.9, '140-149': 58.99, '150-159': 77.74, '160-169': 73.49, '170-179': 78.86, 'old': 81.11, 'new': 78.86}
2025-07-17 13:27:05,671 [trainer.py] => CNN HM: [0.0, 86.546, 76.704, 75.342, 81.804, 73.701, 80.569, 78.414, 79.969]
2025-07-17 13:27:05,671 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45, 85.25, 84.43, 83.09, 82.56, 81.54, 80.98]
Average Accuracy (CNN): 85.02111111111111
2025-07-17 13:27:05,671 [trainer.py] => Average Accuracy (CNN): 85.02111111111111 

2025-07-17 13:27:05,672 [trainer.py] => All params: 89378881
2025-07-17 13:27:05,673 [trainer.py] => Trainable params: 3580225
2025-07-17 13:27:05,692 [ranpac.py] => Learning on 180-190
2025-07-17 13:27:09,510 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:27:09,519 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:27:09,521 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:27:09,521 [ranpac.py] => [Disentangled KNN] task 9, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:27:09,522 [ranpac.py] => [Disentangled KNN] task 9, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:27:09,522 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:27:09,590 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:27:11,319 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:27:48,169 [trainer.py] => No NME accuracy.
2025-07-17 13:27:48,169 [trainer.py] => CNN: {'total': 80.23, '00-99': 87.49, '100-109': 83.78, '110-119': 64.08, '120-129': 64.83, '130-139': 75.17, '140-149': 59.35, '150-159': 78.08, '160-169': 72.82, '170-179': 79.19, '180-189': 71.43, 'old': 80.71, 'new': 71.43}
2025-07-17 13:27:48,169 [trainer.py] => CNN HM: [0.0, 86.546, 76.704, 75.342, 81.804, 73.701, 80.569, 78.414, 79.969, 75.787]
2025-07-17 13:27:48,169 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45, 85.25, 84.43, 83.09, 82.56, 81.54, 80.98, 80.23]
Average Accuracy (CNN): 84.542
2025-07-17 13:27:48,169 [trainer.py] => Average Accuracy (CNN): 84.542 

2025-07-17 13:27:48,171 [trainer.py] => All params: 89478881
2025-07-17 13:27:48,173 [trainer.py] => Trainable params: 3680225
2025-07-17 13:27:48,190 [ranpac.py] => Learning on 190-200
2025-07-17 13:27:52,840 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-17 13:27:52,852 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-17 13:27:52,853 [ranpac.py] => [Dynamic-K] Computed K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:27:52,854 [ranpac.py] => [Disentangled KNN] task 10, dynamic K values: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
2025-07-17 13:27:52,855 [ranpac.py] => [Disentangled KNN] task 10, weight sparsity: 0.970, distance_metric: cosine
2025-07-17 13:27:52,855 [ranpac.py] => [Disentangled Adapter] Using identity features for KNN weight computation: cur_identity shape torch.Size([10, 384]), base_identity shape torch.Size([100, 384])
2025-07-17 13:27:52,924 [ranpac.py] => [Disentangled Covariance] Calibrated 10 covariance matrices using disentangled features
2025-07-17 13:27:54,665 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-17 13:28:21,964 [trainer.py] => No NME accuracy.
2025-07-17 13:28:21,965 [trainer.py] => CNN: {'total': 79.32, '00-99': 87.14, '100-109': 83.78, '110-119': 64.08, '120-129': 64.48, '130-139': 75.17, '140-149': 60.07, '150-159': 78.42, '160-169': 73.15, '170-179': 78.19, '180-189': 71.43, '190-199': 65.88, 'old': 80.05, 'new': 65.88}
2025-07-17 13:28:21,965 [trainer.py] => CNN HM: [0.0, 86.546, 76.704, 75.342, 81.804, 73.701, 80.569, 78.414, 79.969, 75.787, 72.277]
2025-07-17 13:28:21,965 [trainer.py] => CNN top1 curve: [90.61, 89.28, 87.45, 85.25, 84.43, 83.09, 82.56, 81.54, 80.98, 80.23, 79.32]
Average Accuracy (CNN): 84.06727272727272
2025-07-17 13:28:21,966 [trainer.py] => Average Accuracy (CNN): 84.06727272727272 

Accuracy Matrix (CNN):
[[90.61 89.88 89.88 89.12 88.87 88.53 88.42 88.15 87.8  87.49 87.14]
 [ 0.   83.45 83.11 84.12 83.45 84.46 83.11 84.12 84.12 83.78 83.78]
 [ 0.    0.   67.25 66.2  66.55 65.85 65.49 64.08 64.08 64.08 64.08]
 [ 0.    0.    0.   66.55 64.14 64.83 64.48 64.83 64.83 64.83 64.48]
 [ 0.    0.    0.    0.   78.97 79.66 80.   78.28 76.9  75.17 75.17]
 [ 0.    0.    0.    0.    0.   65.47 64.39 60.07 58.99 59.35 60.07]
 [ 0.    0.    0.    0.    0.    0.   78.42 77.4  77.74 78.08 78.42]
 [ 0.    0.    0.    0.    0.    0.    0.   75.17 73.49 72.82 73.15]
 [ 0.    0.    0.    0.    0.    0.    0.    0.   78.86 79.19 78.19]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.   71.43 71.43]
 [ 0.    0.    0.    0.    0.    0.    0.    0.    0.    0.   65.88]]
2025-07-17 13:28:21,967 [trainer.py] => Forgetting (CNN): 2.263999999999998
