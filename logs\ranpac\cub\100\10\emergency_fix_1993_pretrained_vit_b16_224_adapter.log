2025-07-16 20:11:33,235 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 20:11:33,236 [trainer.py] => prefix: emergency_fix
2025-07-16 20:11:33,236 [trainer.py] => dataset: cub
2025-07-16 20:11:33,236 [trainer.py] => memory_size: 0
2025-07-16 20:11:33,236 [trainer.py] => shuffle: True
2025-07-16 20:11:33,236 [trainer.py] => init_cls: 100
2025-07-16 20:11:33,236 [trainer.py] => increment: 10
2025-07-16 20:11:33,236 [trainer.py] => model_name: ranpac
2025-07-16 20:11:33,236 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 20:11:33,236 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 20:11:33,236 [trainer.py] => seed: 1993
2025-07-16 20:11:33,236 [trainer.py] => resume: False
2025-07-16 20:11:33,237 [trainer.py] => shot: 5
2025-07-16 20:11:33,237 [trainer.py] => use_simplecil: False
2025-07-16 20:11:33,237 [trainer.py] => tuned_epoch: 1
2025-07-16 20:11:33,237 [trainer.py] => init_lr: 0.01
2025-07-16 20:11:33,237 [trainer.py] => batch_size: 48
2025-07-16 20:11:33,237 [trainer.py] => weight_decay: 0.0005
2025-07-16 20:11:33,237 [trainer.py] => min_lr: 0
2025-07-16 20:11:33,237 [trainer.py] => ffn_num: 64
2025-07-16 20:11:33,237 [trainer.py] => optimizer: sgd
2025-07-16 20:11:33,237 [trainer.py] => use_RP: True
2025-07-16 20:11:33,237 [trainer.py] => M: 10000
2025-07-16 20:11:33,237 [trainer.py] => fecam: False
2025-07-16 20:11:33,237 [trainer.py] => calibration: True
2025-07-16 20:11:33,238 [trainer.py] => knn_k: 5
2025-07-16 20:11:33,238 [trainer.py] => knn_distance_metric: cosine
2025-07-16 20:11:33,238 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 20:11:33,238 [trainer.py] => knn_adaptive_k: True
2025-07-16 20:11:33,238 [trainer.py] => knn_temperature: 16.0
2025-07-16 20:11:33,238 [trainer.py] => k_min: 3
2025-07-16 20:11:33,238 [trainer.py] => k_max: 21
2025-07-16 20:11:33,238 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 20:11:33,238 [trainer.py] => cosine_temperature: 16.0
2025-07-16 20:11:33,238 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 20:11:33,238 [trainer.py] => use_disentangled_adapter: False
2025-07-16 20:11:33,238 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 20:11:33,381 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 20:11:53,361 [ranpac.py] => [Disentangled Adapter] Disabled - using original RANPAC implementation
2025-07-16 20:11:53,362 [trainer.py] => All params: 86988288
2025-07-16 20:11:53,363 [trainer.py] => Trainable params: 1189632
2025-07-16 20:11:55,113 [ranpac.py] => Learning on 0-100
2025-07-16 20:12:33,186 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-16 20:13:04,487 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 20:13:05,170 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-16 20:13:05,170 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 20:13:05,171 [trainer.py] => No NME accuracy.
2025-07-16 20:13:05,171 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-16 20:13:05,171 [trainer.py] => CNN HM: [0.0]
2025-07-16 20:13:05,171 [trainer.py] => CNN top1 curve: [90.43]
2025-07-16 20:13:05,171 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-16 20:13:05,172 [trainer.py] => All params: 87988289
2025-07-16 20:13:05,173 [trainer.py] => Trainable params: 1189633
2025-07-16 20:13:05,186 [ranpac.py] => Learning on 100-110
2025-07-16 20:13:07,784 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:13:07,788 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:13:07,788 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:13:07,790 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-16 20:13:07,790 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:13:23,706 [trainer.py] => No NME accuracy.
2025-07-16 20:13:23,706 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.84, '100-109': 87.5, 'old': 89.84, 'new': 87.5}
2025-07-16 20:13:23,706 [trainer.py] => CNN HM: [0.0, 88.655]
2025-07-16 20:13:23,706 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-16 20:13:23,706 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-16 20:13:23,708 [trainer.py] => All params: 88088289
2025-07-16 20:13:23,710 [trainer.py] => Trainable params: 2289633
2025-07-16 20:13:23,726 [ranpac.py] => Learning on 110-120
2025-07-16 20:13:25,571 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:13:25,574 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:13:25,574 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:13:25,575 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-16 20:13:25,575 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:13:40,749 [trainer.py] => No NME accuracy.
2025-07-16 20:13:40,750 [trainer.py] => CNN: {'total': 88.11, '00-99': 89.64, '100-109': 88.18, '110-119': 72.54, 'old': 89.5, 'new': 72.54}
2025-07-16 20:13:40,750 [trainer.py] => CNN HM: [0.0, 88.655, 80.132]
2025-07-16 20:13:40,750 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11]
2025-07-16 20:13:40,750 [trainer.py] => Average Accuracy (CNN): 89.39 

2025-07-16 20:13:40,752 [trainer.py] => All params: 88188289
2025-07-16 20:13:40,753 [trainer.py] => Trainable params: 2389633
2025-07-16 20:13:40,771 [ranpac.py] => Learning on 120-130
2025-07-16 20:13:43,624 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:13:43,626 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-16 20:13:43,626 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-16 20:13:43,627 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: cosine
2025-07-16 20:13:43,627 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:14:00,197 [trainer.py] => No NME accuracy.
2025-07-16 20:14:00,198 [trainer.py] => CNN: {'total': 86.39, '00-99': 88.87, '100-109': 89.53, '110-119': 73.59, '120-129': 71.03, 'old': 87.68, 'new': 71.03}
2025-07-16 20:14:00,198 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482]
2025-07-16 20:14:00,198 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39]
2025-07-16 20:14:00,198 [trainer.py] => Average Accuracy (CNN): 88.64 

2025-07-16 20:14:00,199 [trainer.py] => All params: 88288289
2025-07-16 20:14:00,200 [trainer.py] => Trainable params: 2489633
2025-07-16 20:14:00,219 [ranpac.py] => Learning on 130-140
2025-07-16 20:14:03,335 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:14:03,337 [ranpac.py] => [Dynamic-K] Computed K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-16 20:14:03,337 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-16 20:14:03,338 [ranpac.py] => [KNN] task 4, weight sparsity: 0.900, distance_metric: cosine
2025-07-16 20:14:03,338 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:14:20,305 [trainer.py] => No NME accuracy.
2025-07-16 20:14:20,306 [trainer.py] => CNN: {'total': 85.74, '00-99': 88.77, '100-109': 89.86, '110-119': 73.59, '120-129': 67.59, '130-139': 81.38, 'old': 86.07, 'new': 81.38}
2025-07-16 20:14:20,306 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659]
2025-07-16 20:14:20,306 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74]
2025-07-16 20:14:20,306 [trainer.py] => Average Accuracy (CNN): 88.06 

2025-07-16 20:14:20,308 [trainer.py] => All params: 88388289
2025-07-16 20:14:20,309 [trainer.py] => Trainable params: 2589633
2025-07-16 20:14:20,328 [ranpac.py] => Learning on 140-150
2025-07-16 20:14:23,048 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:14:23,050 [ranpac.py] => [Dynamic-K] Computed K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-16 20:14:23,050 [ranpac.py] => [KNN] task 5, dynamic K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-16 20:14:23,051 [ranpac.py] => [KNN] task 5, weight sparsity: 0.908, distance_metric: cosine
2025-07-16 20:14:23,051 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:14:40,850 [trainer.py] => No NME accuracy.
2025-07-16 20:14:40,850 [trainer.py] => CNN: {'total': 84.25, '00-99': 87.9, '100-109': 90.54, '110-119': 71.83, '120-129': 69.66, '130-139': 82.07, '140-149': 69.78, 'old': 85.24, 'new': 69.78}
2025-07-16 20:14:40,850 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739]
2025-07-16 20:14:40,851 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25]
2025-07-16 20:14:40,851 [trainer.py] => Average Accuracy (CNN): 87.425 

2025-07-16 20:14:40,853 [trainer.py] => All params: 88488289
2025-07-16 20:14:40,854 [trainer.py] => Trainable params: 2689633
2025-07-16 20:14:40,873 [ranpac.py] => Learning on 150-160
2025-07-16 20:14:44,110 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:14:44,112 [ranpac.py] => [Dynamic-K] Computed K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-16 20:14:44,112 [ranpac.py] => [KNN] task 6, dynamic K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-16 20:14:44,113 [ranpac.py] => [KNN] task 6, weight sparsity: 0.910, distance_metric: cosine
2025-07-16 20:14:44,113 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:15:30,298 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 20:15:30,299 [trainer.py] => prefix: emergency_fix
2025-07-16 20:15:30,299 [trainer.py] => dataset: cub
2025-07-16 20:15:30,299 [trainer.py] => memory_size: 0
2025-07-16 20:15:30,299 [trainer.py] => shuffle: True
2025-07-16 20:15:30,299 [trainer.py] => init_cls: 100
2025-07-16 20:15:30,299 [trainer.py] => increment: 10
2025-07-16 20:15:30,299 [trainer.py] => model_name: ranpac
2025-07-16 20:15:30,300 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 20:15:30,300 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 20:15:30,300 [trainer.py] => seed: 1993
2025-07-16 20:15:30,300 [trainer.py] => resume: False
2025-07-16 20:15:30,300 [trainer.py] => shot: 5
2025-07-16 20:15:30,300 [trainer.py] => use_simplecil: False
2025-07-16 20:15:30,300 [trainer.py] => tuned_epoch: 1
2025-07-16 20:15:30,300 [trainer.py] => init_lr: 0.01
2025-07-16 20:15:30,301 [trainer.py] => batch_size: 48
2025-07-16 20:15:30,301 [trainer.py] => weight_decay: 0.0005
2025-07-16 20:15:30,301 [trainer.py] => min_lr: 0
2025-07-16 20:15:30,301 [trainer.py] => ffn_num: 64
2025-07-16 20:15:30,301 [trainer.py] => optimizer: sgd
2025-07-16 20:15:30,301 [trainer.py] => use_RP: True
2025-07-16 20:15:30,301 [trainer.py] => M: 10000
2025-07-16 20:15:30,301 [trainer.py] => fecam: False
2025-07-16 20:15:30,301 [trainer.py] => calibration: True
2025-07-16 20:15:30,302 [trainer.py] => knn_k: 5
2025-07-16 20:15:30,302 [trainer.py] => knn_distance_metric: cosine
2025-07-16 20:15:30,302 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 20:15:30,302 [trainer.py] => knn_adaptive_k: True
2025-07-16 20:15:30,302 [trainer.py] => knn_temperature: 16.0
2025-07-16 20:15:30,302 [trainer.py] => k_min: 3
2025-07-16 20:15:30,302 [trainer.py] => k_max: 21
2025-07-16 20:15:30,302 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 20:15:30,303 [trainer.py] => cosine_temperature: 16.0
2025-07-16 20:15:30,303 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 20:15:30,303 [trainer.py] => use_disentangled_adapter: False
2025-07-16 20:15:30,303 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 20:15:30,454 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 20:15:53,872 [ranpac.py] => [Disentangled Adapter] Disabled - using original RANPAC implementation
2025-07-16 20:15:53,873 [trainer.py] => All params: 86988288
2025-07-16 20:15:53,874 [trainer.py] => Trainable params: 1189632
2025-07-16 20:15:55,627 [ranpac.py] => Learning on 0-100
2025-07-16 20:16:30,152 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-16 20:17:00,321 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 20:17:00,986 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-16 20:17:00,986 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 20:17:00,987 [trainer.py] => No NME accuracy.
2025-07-16 20:17:00,987 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-16 20:17:00,987 [trainer.py] => CNN HM: [0.0]
2025-07-16 20:17:00,987 [trainer.py] => CNN top1 curve: [90.43]
2025-07-16 20:17:00,987 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-16 20:17:00,988 [trainer.py] => All params: 87988289
2025-07-16 20:17:00,989 [trainer.py] => Trainable params: 1189633
2025-07-16 20:17:01,006 [ranpac.py] => Learning on 100-110
2025-07-16 20:17:02,925 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:17:02,928 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:17:02,928 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:17:02,929 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-16 20:17:02,929 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:17:17,243 [trainer.py] => No NME accuracy.
2025-07-16 20:17:17,244 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.84, '100-109': 87.5, 'old': 89.84, 'new': 87.5}
2025-07-16 20:17:17,244 [trainer.py] => CNN HM: [0.0, 88.655]
2025-07-16 20:17:17,244 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-16 20:17:17,245 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-16 20:17:17,246 [trainer.py] => All params: 88088289
2025-07-16 20:17:17,247 [trainer.py] => Trainable params: 2289633
2025-07-16 20:17:17,263 [ranpac.py] => Learning on 110-120
2025-07-16 20:17:19,627 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:17:19,629 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:17:19,630 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:17:19,631 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-16 20:17:19,631 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:17:34,482 [trainer.py] => No NME accuracy.
2025-07-16 20:17:34,483 [trainer.py] => CNN: {'total': 88.11, '00-99': 89.64, '100-109': 88.18, '110-119': 72.54, 'old': 89.5, 'new': 72.54}
2025-07-16 20:17:34,483 [trainer.py] => CNN HM: [0.0, 88.655, 80.132]
2025-07-16 20:17:34,483 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11]
2025-07-16 20:17:34,483 [trainer.py] => Average Accuracy (CNN): 89.39 

2025-07-16 20:17:34,485 [trainer.py] => All params: 88188289
2025-07-16 20:17:34,487 [trainer.py] => Trainable params: 2389633
2025-07-16 20:17:34,503 [ranpac.py] => Learning on 120-130
2025-07-16 20:17:36,446 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:17:36,448 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-16 20:17:36,448 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-16 20:17:36,449 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: cosine
2025-07-16 20:17:36,449 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:17:52,193 [trainer.py] => No NME accuracy.
2025-07-16 20:17:52,193 [trainer.py] => CNN: {'total': 86.39, '00-99': 88.87, '100-109': 89.53, '110-119': 73.59, '120-129': 71.03, 'old': 87.68, 'new': 71.03}
2025-07-16 20:17:52,193 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482]
2025-07-16 20:17:52,193 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39]
2025-07-16 20:17:52,194 [trainer.py] => Average Accuracy (CNN): 88.64 

2025-07-16 20:17:52,195 [trainer.py] => All params: 88288289
2025-07-16 20:17:52,196 [trainer.py] => Trainable params: 2489633
2025-07-16 20:17:52,214 [ranpac.py] => Learning on 130-140
2025-07-16 20:17:54,021 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:17:54,023 [ranpac.py] => [Dynamic-K] Computed K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-16 20:17:54,023 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-16 20:17:54,024 [ranpac.py] => [KNN] task 4, weight sparsity: 0.900, distance_metric: cosine
2025-07-16 20:17:54,024 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:18:23,099 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 20:18:23,100 [trainer.py] => prefix: emergency_fix
2025-07-16 20:18:23,100 [trainer.py] => dataset: cub
2025-07-16 20:18:23,100 [trainer.py] => memory_size: 0
2025-07-16 20:18:23,100 [trainer.py] => shuffle: True
2025-07-16 20:18:23,100 [trainer.py] => init_cls: 100
2025-07-16 20:18:23,100 [trainer.py] => increment: 10
2025-07-16 20:18:23,101 [trainer.py] => model_name: ranpac
2025-07-16 20:18:23,101 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 20:18:23,101 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 20:18:23,101 [trainer.py] => seed: 1993
2025-07-16 20:18:23,101 [trainer.py] => resume: False
2025-07-16 20:18:23,101 [trainer.py] => shot: 5
2025-07-16 20:18:23,101 [trainer.py] => use_simplecil: False
2025-07-16 20:18:23,102 [trainer.py] => tuned_epoch: 1
2025-07-16 20:18:23,102 [trainer.py] => init_lr: 0.01
2025-07-16 20:18:23,102 [trainer.py] => batch_size: 48
2025-07-16 20:18:23,102 [trainer.py] => weight_decay: 0.0005
2025-07-16 20:18:23,102 [trainer.py] => min_lr: 0
2025-07-16 20:18:23,102 [trainer.py] => ffn_num: 64
2025-07-16 20:18:23,102 [trainer.py] => optimizer: sgd
2025-07-16 20:18:23,102 [trainer.py] => use_RP: True
2025-07-16 20:18:23,103 [trainer.py] => M: 10000
2025-07-16 20:18:23,103 [trainer.py] => fecam: False
2025-07-16 20:18:23,103 [trainer.py] => calibration: True
2025-07-16 20:18:23,103 [trainer.py] => knn_k: 5
2025-07-16 20:18:23,103 [trainer.py] => knn_distance_metric: cosine
2025-07-16 20:18:23,103 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 20:18:23,103 [trainer.py] => knn_adaptive_k: True
2025-07-16 20:18:23,103 [trainer.py] => knn_temperature: 16.0
2025-07-16 20:18:23,104 [trainer.py] => k_min: 3
2025-07-16 20:18:23,104 [trainer.py] => k_max: 21
2025-07-16 20:18:23,104 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 20:18:23,104 [trainer.py] => cosine_temperature: 16.0
2025-07-16 20:18:23,104 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 20:18:23,104 [trainer.py] => use_disentangled_adapter: False
2025-07-16 20:18:23,104 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 20:18:23,193 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 20:18:36,169 [ranpac.py] => [Disentangled Adapter] Disabled - using original RANPAC implementation
2025-07-16 20:18:36,170 [trainer.py] => All params: 86988288
2025-07-16 20:18:36,171 [trainer.py] => Trainable params: 1189632
2025-07-16 20:18:38,086 [ranpac.py] => Learning on 0-100
2025-07-16 20:19:09,098 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-16 20:19:36,476 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 20:19:37,143 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-16 20:19:37,144 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 20:19:37,144 [trainer.py] => No NME accuracy.
2025-07-16 20:19:37,144 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-16 20:19:37,144 [trainer.py] => CNN HM: [0.0]
2025-07-16 20:19:37,144 [trainer.py] => CNN top1 curve: [90.43]
2025-07-16 20:19:37,144 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-16 20:19:37,145 [trainer.py] => All params: 87988289
2025-07-16 20:19:37,146 [trainer.py] => Trainable params: 1189633
2025-07-16 20:19:37,158 [ranpac.py] => Learning on 100-110
2025-07-16 20:19:38,752 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:19:38,754 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:19:38,754 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:19:38,755 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-16 20:19:38,755 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:19:53,438 [trainer.py] => No NME accuracy.
2025-07-16 20:19:53,439 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.84, '100-109': 87.5, 'old': 89.84, 'new': 87.5}
2025-07-16 20:19:53,439 [trainer.py] => CNN HM: [0.0, 88.655]
2025-07-16 20:19:53,439 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-16 20:19:53,439 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-16 20:19:53,441 [trainer.py] => All params: 88088289
2025-07-16 20:19:53,442 [trainer.py] => Trainable params: 2289633
2025-07-16 20:19:53,463 [ranpac.py] => Learning on 110-120
2025-07-16 20:19:54,832 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:19:54,834 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:19:54,834 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:19:54,835 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-16 20:19:54,835 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:20:13,404 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 20:20:13,405 [trainer.py] => prefix: emergency_fix
2025-07-16 20:20:13,405 [trainer.py] => dataset: cub
2025-07-16 20:20:13,405 [trainer.py] => memory_size: 0
2025-07-16 20:20:13,405 [trainer.py] => shuffle: True
2025-07-16 20:20:13,405 [trainer.py] => init_cls: 100
2025-07-16 20:20:13,405 [trainer.py] => increment: 10
2025-07-16 20:20:13,405 [trainer.py] => model_name: ranpac
2025-07-16 20:20:13,406 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 20:20:13,406 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 20:20:13,406 [trainer.py] => seed: 1993
2025-07-16 20:20:13,406 [trainer.py] => resume: False
2025-07-16 20:20:13,406 [trainer.py] => shot: 5
2025-07-16 20:20:13,406 [trainer.py] => use_simplecil: False
2025-07-16 20:20:13,406 [trainer.py] => tuned_epoch: 1
2025-07-16 20:20:13,406 [trainer.py] => init_lr: 0.01
2025-07-16 20:20:13,406 [trainer.py] => batch_size: 48
2025-07-16 20:20:13,407 [trainer.py] => weight_decay: 0.0005
2025-07-16 20:20:13,407 [trainer.py] => min_lr: 0
2025-07-16 20:20:13,407 [trainer.py] => ffn_num: 64
2025-07-16 20:20:13,407 [trainer.py] => optimizer: sgd
2025-07-16 20:20:13,407 [trainer.py] => use_RP: True
2025-07-16 20:20:13,407 [trainer.py] => M: 10000
2025-07-16 20:20:13,407 [trainer.py] => fecam: False
2025-07-16 20:20:13,407 [trainer.py] => calibration: True
2025-07-16 20:20:13,407 [trainer.py] => knn_k: 5
2025-07-16 20:20:13,407 [trainer.py] => knn_distance_metric: cosine
2025-07-16 20:20:13,408 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 20:20:13,408 [trainer.py] => knn_adaptive_k: True
2025-07-16 20:20:13,408 [trainer.py] => knn_temperature: 16.0
2025-07-16 20:20:13,408 [trainer.py] => k_min: 3
2025-07-16 20:20:13,408 [trainer.py] => k_max: 21
2025-07-16 20:20:13,408 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 20:20:13,408 [trainer.py] => cosine_temperature: 16.0
2025-07-16 20:20:13,408 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 20:20:13,408 [trainer.py] => use_disentangled_adapter: False
2025-07-16 20:20:13,409 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 20:20:13,484 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 20:20:19,942 [ranpac.py] => [Disentangled Adapter] Disabled - using original RANPAC implementation
2025-07-16 20:20:19,943 [trainer.py] => All params: 86988288
2025-07-16 20:20:19,943 [trainer.py] => Trainable params: 1189632
2025-07-16 20:20:21,817 [ranpac.py] => Learning on 0-100
2025-07-16 20:20:52,441 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-16 20:21:25,885 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 20:21:26,555 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-16 20:21:26,556 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 20:21:26,556 [trainer.py] => No NME accuracy.
2025-07-16 20:21:26,556 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-16 20:21:26,556 [trainer.py] => CNN HM: [0.0]
2025-07-16 20:21:26,556 [trainer.py] => CNN top1 curve: [90.43]
2025-07-16 20:21:26,556 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-16 20:21:26,557 [trainer.py] => All params: 87988289
2025-07-16 20:21:26,558 [trainer.py] => Trainable params: 1189633
2025-07-16 20:21:26,583 [ranpac.py] => Learning on 100-110
2025-07-16 20:21:28,248 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:21:28,250 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:21:28,250 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:21:28,251 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-16 20:21:28,251 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:21:42,406 [trainer.py] => No NME accuracy.
2025-07-16 20:21:42,406 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.84, '100-109': 87.5, 'old': 89.84, 'new': 87.5}
2025-07-16 20:21:42,406 [trainer.py] => CNN HM: [0.0, 88.655]
2025-07-16 20:21:42,406 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-16 20:21:42,406 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-16 20:21:42,407 [trainer.py] => All params: 88088289
2025-07-16 20:21:42,408 [trainer.py] => Trainable params: 2289633
2025-07-16 20:21:42,430 [ranpac.py] => Learning on 110-120
2025-07-16 20:21:44,468 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:21:44,469 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:21:44,470 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:21:44,470 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-16 20:21:44,470 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:21:58,939 [trainer.py] => No NME accuracy.
2025-07-16 20:21:58,940 [trainer.py] => CNN: {'total': 88.11, '00-99': 89.64, '100-109': 88.18, '110-119': 72.54, 'old': 89.5, 'new': 72.54}
2025-07-16 20:21:58,940 [trainer.py] => CNN HM: [0.0, 88.655, 80.132]
2025-07-16 20:21:58,940 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11]
2025-07-16 20:21:58,940 [trainer.py] => Average Accuracy (CNN): 89.39 

2025-07-16 20:21:58,942 [trainer.py] => All params: 88188289
2025-07-16 20:21:58,944 [trainer.py] => Trainable params: 2389633
2025-07-16 20:21:58,961 [ranpac.py] => Learning on 120-130
2025-07-16 20:22:01,144 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:22:01,146 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-16 20:22:01,146 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-16 20:22:01,147 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: cosine
2025-07-16 20:22:01,147 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:22:47,286 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 20:22:47,287 [trainer.py] => prefix: emergency_fix
2025-07-16 20:22:47,287 [trainer.py] => dataset: cub
2025-07-16 20:22:47,287 [trainer.py] => memory_size: 0
2025-07-16 20:22:47,287 [trainer.py] => shuffle: True
2025-07-16 20:22:47,287 [trainer.py] => init_cls: 100
2025-07-16 20:22:47,287 [trainer.py] => increment: 10
2025-07-16 20:22:47,287 [trainer.py] => model_name: ranpac
2025-07-16 20:22:47,287 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 20:22:47,287 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 20:22:47,287 [trainer.py] => seed: 1993
2025-07-16 20:22:47,288 [trainer.py] => resume: False
2025-07-16 20:22:47,288 [trainer.py] => shot: 5
2025-07-16 20:22:47,288 [trainer.py] => use_simplecil: False
2025-07-16 20:22:47,288 [trainer.py] => tuned_epoch: 1
2025-07-16 20:22:47,288 [trainer.py] => init_lr: 0.01
2025-07-16 20:22:47,288 [trainer.py] => batch_size: 48
2025-07-16 20:22:47,288 [trainer.py] => weight_decay: 0.0005
2025-07-16 20:22:47,288 [trainer.py] => min_lr: 0
2025-07-16 20:22:47,288 [trainer.py] => ffn_num: 64
2025-07-16 20:22:47,289 [trainer.py] => optimizer: sgd
2025-07-16 20:22:47,289 [trainer.py] => use_RP: True
2025-07-16 20:22:47,289 [trainer.py] => M: 10000
2025-07-16 20:22:47,289 [trainer.py] => fecam: False
2025-07-16 20:22:47,289 [trainer.py] => calibration: True
2025-07-16 20:22:47,289 [trainer.py] => knn_k: 5
2025-07-16 20:22:47,289 [trainer.py] => knn_distance_metric: cosine
2025-07-16 20:22:47,289 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 20:22:47,289 [trainer.py] => knn_adaptive_k: True
2025-07-16 20:22:47,289 [trainer.py] => knn_temperature: 16.0
2025-07-16 20:22:47,290 [trainer.py] => k_min: 3
2025-07-16 20:22:47,290 [trainer.py] => k_max: 21
2025-07-16 20:22:47,290 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 20:22:47,290 [trainer.py] => cosine_temperature: 16.0
2025-07-16 20:22:47,290 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 20:22:47,290 [trainer.py] => use_disentangled_adapter: False
2025-07-16 20:22:47,290 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 20:22:47,381 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 20:23:02,596 [ranpac.py] => [Disentangled Adapter] Disabled - using original RANPAC implementation
2025-07-16 20:23:02,597 [trainer.py] => All params: 86988288
2025-07-16 20:23:02,597 [trainer.py] => Trainable params: 1189632
2025-07-16 20:23:04,316 [ranpac.py] => Learning on 0-100
2025-07-16 20:23:35,853 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-16 20:24:07,337 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 20:24:08,006 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-16 20:24:08,007 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 20:24:08,007 [trainer.py] => No NME accuracy.
2025-07-16 20:24:08,007 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-16 20:24:08,007 [trainer.py] => CNN HM: [0.0]
2025-07-16 20:24:08,007 [trainer.py] => CNN top1 curve: [90.43]
2025-07-16 20:24:08,007 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-16 20:24:08,008 [trainer.py] => All params: 87988289
2025-07-16 20:24:08,009 [trainer.py] => Trainable params: 1189633
2025-07-16 20:24:08,029 [ranpac.py] => Learning on 100-110
2025-07-16 20:24:09,667 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:24:09,668 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:24:09,668 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-16 20:24:09,669 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-16 20:24:09,670 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:24:24,232 [trainer.py] => No NME accuracy.
2025-07-16 20:24:24,232 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.84, '100-109': 87.5, 'old': 89.84, 'new': 87.5}
2025-07-16 20:24:24,232 [trainer.py] => CNN HM: [0.0, 88.655]
2025-07-16 20:24:24,232 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-16 20:24:24,232 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-16 20:24:24,233 [trainer.py] => All params: 88088289
2025-07-16 20:24:24,234 [trainer.py] => Trainable params: 2289633
2025-07-16 20:24:24,248 [ranpac.py] => Learning on 110-120
2025-07-16 20:24:25,641 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:24:25,644 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:24:25,644 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-16 20:24:25,645 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-16 20:24:25,645 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:24:39,946 [trainer.py] => No NME accuracy.
2025-07-16 20:24:39,946 [trainer.py] => CNN: {'total': 88.11, '00-99': 89.64, '100-109': 88.18, '110-119': 72.54, 'old': 89.5, 'new': 72.54}
2025-07-16 20:24:39,946 [trainer.py] => CNN HM: [0.0, 88.655, 80.132]
2025-07-16 20:24:39,947 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11]
2025-07-16 20:24:39,947 [trainer.py] => Average Accuracy (CNN): 89.39 

2025-07-16 20:24:39,948 [trainer.py] => All params: 88188289
2025-07-16 20:24:39,949 [trainer.py] => Trainable params: 2389633
2025-07-16 20:24:39,963 [ranpac.py] => Learning on 120-130
2025-07-16 20:24:42,220 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:24:42,222 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-16 20:24:42,223 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-16 20:24:42,223 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: cosine
2025-07-16 20:24:42,223 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:24:58,233 [trainer.py] => No NME accuracy.
2025-07-16 20:24:58,234 [trainer.py] => CNN: {'total': 86.39, '00-99': 88.87, '100-109': 89.53, '110-119': 73.59, '120-129': 71.03, 'old': 87.68, 'new': 71.03}
2025-07-16 20:24:58,234 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482]
2025-07-16 20:24:58,234 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39]
2025-07-16 20:24:58,234 [trainer.py] => Average Accuracy (CNN): 88.64 

2025-07-16 20:24:58,236 [trainer.py] => All params: 88288289
2025-07-16 20:24:58,237 [trainer.py] => Trainable params: 2489633
2025-07-16 20:24:58,254 [ranpac.py] => Learning on 130-140
2025-07-16 20:25:00,618 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:25:00,620 [ranpac.py] => [Dynamic-K] Computed K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-16 20:25:00,620 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-16 20:25:00,621 [ranpac.py] => [KNN] task 4, weight sparsity: 0.900, distance_metric: cosine
2025-07-16 20:25:00,621 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:25:16,964 [trainer.py] => No NME accuracy.
2025-07-16 20:25:16,965 [trainer.py] => CNN: {'total': 85.74, '00-99': 88.77, '100-109': 89.86, '110-119': 73.59, '120-129': 67.59, '130-139': 81.38, 'old': 86.07, 'new': 81.38}
2025-07-16 20:25:16,965 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659]
2025-07-16 20:25:16,965 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74]
2025-07-16 20:25:16,966 [trainer.py] => Average Accuracy (CNN): 88.06 

2025-07-16 20:25:16,967 [trainer.py] => All params: 88388289
2025-07-16 20:25:16,968 [trainer.py] => Trainable params: 2589633
2025-07-16 20:25:16,986 [ranpac.py] => Learning on 140-150
2025-07-16 20:25:19,005 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:25:19,007 [ranpac.py] => [Dynamic-K] Computed K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-16 20:25:19,008 [ranpac.py] => [KNN] task 5, dynamic K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-16 20:25:19,008 [ranpac.py] => [KNN] task 5, weight sparsity: 0.908, distance_metric: cosine
2025-07-16 20:25:19,008 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:26:36,407 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 20:26:36,407 [trainer.py] => prefix: emergency_fix
2025-07-16 20:26:36,408 [trainer.py] => dataset: cub
2025-07-16 20:26:36,408 [trainer.py] => memory_size: 0
2025-07-16 20:26:36,408 [trainer.py] => shuffle: True
2025-07-16 20:26:36,408 [trainer.py] => init_cls: 100
2025-07-16 20:26:36,408 [trainer.py] => increment: 10
2025-07-16 20:26:36,408 [trainer.py] => model_name: ranpac
2025-07-16 20:26:36,408 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 20:26:36,408 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 20:26:36,408 [trainer.py] => seed: 1993
2025-07-16 20:26:36,408 [trainer.py] => resume: False
2025-07-16 20:26:36,408 [trainer.py] => shot: 5
2025-07-16 20:26:36,408 [trainer.py] => use_simplecil: False
2025-07-16 20:26:36,408 [trainer.py] => tuned_epoch: 1
2025-07-16 20:26:36,408 [trainer.py] => init_lr: 0.01
2025-07-16 20:26:36,408 [trainer.py] => batch_size: 48
2025-07-16 20:26:36,409 [trainer.py] => weight_decay: 0.0005
2025-07-16 20:26:36,409 [trainer.py] => min_lr: 0
2025-07-16 20:26:36,409 [trainer.py] => ffn_num: 64
2025-07-16 20:26:36,409 [trainer.py] => optimizer: sgd
2025-07-16 20:26:36,409 [trainer.py] => use_RP: True
2025-07-16 20:26:36,409 [trainer.py] => M: 10000
2025-07-16 20:26:36,409 [trainer.py] => fecam: False
2025-07-16 20:26:36,409 [trainer.py] => calibration: True
2025-07-16 20:26:36,409 [trainer.py] => knn_k: 5
2025-07-16 20:26:36,409 [trainer.py] => knn_distance_metric: cosine
2025-07-16 20:26:36,409 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 20:26:36,409 [trainer.py] => knn_adaptive_k: True
2025-07-16 20:26:36,409 [trainer.py] => knn_temperature: 16.0
2025-07-16 20:26:36,409 [trainer.py] => k_min: 3
2025-07-16 20:26:36,409 [trainer.py] => k_max: 21
2025-07-16 20:26:36,409 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 20:26:36,410 [trainer.py] => cosine_temperature: 16.0
2025-07-16 20:26:36,410 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 20:26:36,410 [trainer.py] => use_disentangled_adapter: True
2025-07-16 20:26:36,410 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 20:26:36,497 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 20:26:55,210 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 20:26:55,210 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 20:26:55,210 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 20:26:55,210 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 20:26:55,210 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 20:26:55,211 [trainer.py] => All params: 87578880
2025-07-16 20:26:55,212 [trainer.py] => Trainable params: 1780224
2025-07-16 20:26:56,872 [ranpac.py] => Learning on 0-100
2025-07-16 20:27:31,482 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.592, Train_accy 3.20, Test_accy 8.49
2025-07-16 20:27:42,662 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-16 20:27:56,557 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 20:27:57,217 [ranpac.py] => [Dynamic-K] Base similarity range: [7.0713, 10.8340]
2025-07-16 20:27:57,218 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 20:27:57,218 [trainer.py] => No NME accuracy.
2025-07-16 20:27:57,218 [trainer.py] => CNN: {'total': 90.61, '00-99': 90.61, 'old': 0, 'new': 90.61}
2025-07-16 20:27:57,218 [trainer.py] => CNN HM: [0.0]
2025-07-16 20:27:57,218 [trainer.py] => CNN top1 curve: [90.61]
2025-07-16 20:27:57,218 [trainer.py] => Average Accuracy (CNN): 90.61 

2025-07-16 20:27:57,219 [trainer.py] => All params: 88578881
2025-07-16 20:27:57,220 [trainer.py] => Trainable params: 1780225
2025-07-16 20:27:57,234 [ranpac.py] => Learning on 100-110
2025-07-16 20:27:59,116 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 20:27:59,143 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:27:59,144 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 6, 15, 13, 10, 6]
2025-07-16 20:27:59,145 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 6, 15, 13, 10, 6]
2025-07-16 20:27:59,145 [ranpac.py] => [KNN] task 1, weight sparsity: 0.889, distance_metric: cosine
2025-07-16 20:27:59,146 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:28:02,132 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 20:28:15,121 [trainer.py] => No NME accuracy.
2025-07-16 20:28:15,122 [trainer.py] => CNN: {'total': 89.12, '00-99': 89.64, '100-109': 84.12, 'old': 89.64, 'new': 84.12}
2025-07-16 20:28:15,122 [trainer.py] => CNN HM: [0.0, 86.792]
2025-07-16 20:28:15,122 [trainer.py] => CNN top1 curve: [90.61, 89.12]
2025-07-16 20:28:15,122 [trainer.py] => Average Accuracy (CNN): 89.86500000000001 

2025-07-16 20:28:15,124 [trainer.py] => All params: 88678881
2025-07-16 20:28:15,126 [trainer.py] => Trainable params: 2880225
2025-07-16 20:28:15,142 [ranpac.py] => Learning on 110-120
2025-07-16 20:28:18,287 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 20:28:18,312 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:28:18,314 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 9, 9, 17, 17, 11, 5, 15, 16]
2025-07-16 20:28:18,314 [ranpac.py] => [KNN] task 2, dynamic K values: [4, 12, 9, 9, 17, 17, 11, 5, 15, 16]
2025-07-16 20:28:18,315 [ranpac.py] => [KNN] task 2, weight sparsity: 0.885, distance_metric: cosine
2025-07-16 20:28:18,315 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:28:21,215 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 20:28:33,827 [trainer.py] => No NME accuracy.
2025-07-16 20:28:33,828 [trainer.py] => CNN: {'total': 87.59, '00-99': 89.98, '100-109': 84.46, '110-119': 66.55, 'old': 89.47, 'new': 66.55}
2025-07-16 20:28:33,828 [trainer.py] => CNN HM: [0.0, 86.792, 76.326]
2025-07-16 20:28:33,828 [trainer.py] => CNN top1 curve: [90.61, 89.12, 87.59]
2025-07-16 20:28:33,828 [trainer.py] => Average Accuracy (CNN): 89.10666666666668 

2025-07-16 20:28:33,830 [trainer.py] => All params: 88778881
2025-07-16 20:28:33,832 [trainer.py] => Trainable params: 2980225
2025-07-16 20:28:33,850 [ranpac.py] => Learning on 120-130
2025-07-16 20:28:35,447 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 20:28:35,476 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:28:35,477 [ranpac.py] => [Dynamic-K] Computed K values: [10, 10, 10, 16, 6, 6, 6, 7, 8, 8]
2025-07-16 20:28:35,477 [ranpac.py] => [KNN] task 3, dynamic K values: [10, 10, 10, 16, 6, 6, 6, 7, 8, 8]
2025-07-16 20:28:35,478 [ranpac.py] => [KNN] task 3, weight sparsity: 0.913, distance_metric: cosine
2025-07-16 20:28:35,478 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:28:38,496 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 20:28:53,307 [trainer.py] => No NME accuracy.
2025-07-16 20:28:53,307 [trainer.py] => CNN: {'total': 85.54, '00-99': 89.22, '100-109': 84.46, '110-119': 66.55, '120-129': 68.62, 'old': 86.96, 'new': 68.62}
2025-07-16 20:28:53,308 [trainer.py] => CNN HM: [0.0, 86.792, 76.326, 76.709]
2025-07-16 20:28:53,308 [trainer.py] => CNN top1 curve: [90.61, 89.12, 87.59, 85.54]
2025-07-16 20:28:53,308 [trainer.py] => Average Accuracy (CNN): 88.21500000000002 

2025-07-16 20:28:53,310 [trainer.py] => All params: 88878881
2025-07-16 20:28:53,311 [trainer.py] => Trainable params: 3080225
2025-07-16 20:28:53,327 [ranpac.py] => Learning on 130-140
2025-07-16 20:28:55,367 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 20:28:55,385 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 20:28:55,387 [ranpac.py] => [Dynamic-K] Computed K values: [9, 15, 10, 11, 14, 6, 9, 8, 11, 10]
2025-07-16 20:28:55,387 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 15, 10, 11, 14, 6, 9, 8, 11, 10]
2025-07-16 20:28:55,388 [ranpac.py] => [KNN] task 4, weight sparsity: 0.897, distance_metric: cosine
2025-07-16 20:28:55,388 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 20:28:58,596 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 20:29:22,454 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 20:29:22,455 [trainer.py] => prefix: emergency_fix
2025-07-16 20:29:22,455 [trainer.py] => dataset: cub
2025-07-16 20:29:22,455 [trainer.py] => memory_size: 0
2025-07-16 20:29:22,455 [trainer.py] => shuffle: True
2025-07-16 20:29:22,455 [trainer.py] => init_cls: 100
2025-07-16 20:29:22,455 [trainer.py] => increment: 10
2025-07-16 20:29:22,455 [trainer.py] => model_name: ranpac
2025-07-16 20:29:22,455 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 20:29:22,456 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 20:29:22,456 [trainer.py] => seed: 1993
2025-07-16 20:29:22,456 [trainer.py] => resume: False
2025-07-16 20:29:22,456 [trainer.py] => shot: 5
2025-07-16 20:29:22,456 [trainer.py] => use_simplecil: False
2025-07-16 20:29:22,456 [trainer.py] => tuned_epoch: 1
2025-07-16 20:29:22,456 [trainer.py] => init_lr: 0.01
2025-07-16 20:29:22,456 [trainer.py] => batch_size: 48
2025-07-16 20:29:22,456 [trainer.py] => weight_decay: 0.0005
2025-07-16 20:29:22,457 [trainer.py] => min_lr: 0
2025-07-16 20:29:22,457 [trainer.py] => ffn_num: 64
2025-07-16 20:29:22,457 [trainer.py] => optimizer: sgd
2025-07-16 20:29:22,457 [trainer.py] => use_RP: True
2025-07-16 20:29:22,457 [trainer.py] => M: 10000
2025-07-16 20:29:22,457 [trainer.py] => fecam: False
2025-07-16 20:29:22,457 [trainer.py] => calibration: True
2025-07-16 20:29:22,457 [trainer.py] => knn_k: 5
2025-07-16 20:29:22,457 [trainer.py] => knn_distance_metric: cosine
2025-07-16 20:29:22,457 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 20:29:22,458 [trainer.py] => knn_adaptive_k: True
2025-07-16 20:29:22,458 [trainer.py] => knn_temperature: 16.0
2025-07-16 20:29:22,458 [trainer.py] => k_min: 3
2025-07-16 20:29:22,458 [trainer.py] => k_max: 21
2025-07-16 20:29:22,458 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 20:29:22,458 [trainer.py] => cosine_temperature: 16.0
2025-07-16 20:29:22,458 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 20:29:22,458 [trainer.py] => use_disentangled_adapter: True
2025-07-16 20:29:22,458 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 20:29:22,529 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 20:29:31,083 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 20:29:31,083 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 20:29:31,083 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 20:29:31,083 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 20:29:31,083 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 20:29:31,084 [trainer.py] => All params: 87578880
2025-07-16 20:29:31,085 [trainer.py] => Trainable params: 1780224
2025-07-16 20:29:32,752 [ranpac.py] => Learning on 0-100
2025-07-16 20:29:41,865 [ranpac.py] => [Disentangled Training] Error in disentangled forward pass: CUDA out of memory. Tried to allocate 112.00 MiB (GPU 0; 44.35 GiB total capacity; 4.65 GiB already allocated; 67.94 MiB free; 4.79 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF, falling back to normal training
2025-07-16 20:39:44,778 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 20:39:44,779 [trainer.py] => prefix: emergency_fix
2025-07-16 20:39:44,779 [trainer.py] => dataset: cub
2025-07-16 20:39:44,780 [trainer.py] => memory_size: 0
2025-07-16 20:39:44,780 [trainer.py] => shuffle: True
2025-07-16 20:39:44,781 [trainer.py] => init_cls: 100
2025-07-16 20:39:44,781 [trainer.py] => increment: 10
2025-07-16 20:39:44,781 [trainer.py] => model_name: ranpac
2025-07-16 20:39:44,782 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 20:39:44,782 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 20:39:44,783 [trainer.py] => seed: 1993
2025-07-16 20:39:44,783 [trainer.py] => resume: False
2025-07-16 20:39:44,783 [trainer.py] => shot: 5
2025-07-16 20:39:44,784 [trainer.py] => use_simplecil: False
2025-07-16 20:39:44,784 [trainer.py] => tuned_epoch: 1
2025-07-16 20:39:44,785 [trainer.py] => init_lr: 0.01
2025-07-16 20:39:44,785 [trainer.py] => batch_size: 48
2025-07-16 20:39:44,785 [trainer.py] => weight_decay: 0.0005
2025-07-16 20:39:44,786 [trainer.py] => min_lr: 0
2025-07-16 20:39:44,786 [trainer.py] => ffn_num: 64
2025-07-16 20:39:44,787 [trainer.py] => optimizer: sgd
2025-07-16 20:39:44,787 [trainer.py] => use_RP: True
2025-07-16 20:39:44,787 [trainer.py] => M: 10000
2025-07-16 20:39:44,788 [trainer.py] => fecam: False
2025-07-16 20:39:44,788 [trainer.py] => calibration: True
2025-07-16 20:39:44,789 [trainer.py] => knn_k: 5
2025-07-16 20:39:44,789 [trainer.py] => knn_distance_metric: cosine
2025-07-16 20:39:44,789 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 20:39:44,789 [trainer.py] => knn_adaptive_k: True
2025-07-16 20:39:44,789 [trainer.py] => knn_temperature: 16.0
2025-07-16 20:39:44,790 [trainer.py] => k_min: 3
2025-07-16 20:39:44,790 [trainer.py] => k_max: 21
2025-07-16 20:39:44,790 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 20:39:44,790 [trainer.py] => cosine_temperature: 16.0
2025-07-16 20:39:44,790 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 20:39:44,790 [trainer.py] => use_disentangled_adapter: True
2025-07-16 20:39:44,790 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 20:39:44,939 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 20:40:09,067 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 20:40:09,067 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 20:40:09,067 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 20:40:09,068 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 20:40:09,068 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 20:40:09,069 [trainer.py] => All params: 87578880
2025-07-16 20:40:09,070 [trainer.py] => Trainable params: 1780224
2025-07-16 20:40:10,704 [ranpac.py] => Learning on 0-100
2025-07-16 20:40:15,081 [ranpac.py] => [Disentangled Training] Error in disentangled forward pass: CUDA out of memory. Tried to allocate 112.00 MiB (GPU 0; 44.35 GiB total capacity; 4.65 GiB already allocated; 67.94 MiB free; 4.79 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF, falling back to normal training
2025-07-16 21:06:15,875 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 21:06:15,878 [trainer.py] => prefix: emergency_fix
2025-07-16 21:06:15,878 [trainer.py] => dataset: cub
2025-07-16 21:06:15,878 [trainer.py] => memory_size: 0
2025-07-16 21:06:15,879 [trainer.py] => shuffle: True
2025-07-16 21:06:15,879 [trainer.py] => init_cls: 100
2025-07-16 21:06:15,879 [trainer.py] => increment: 10
2025-07-16 21:06:15,879 [trainer.py] => model_name: ranpac
2025-07-16 21:06:15,879 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:06:15,879 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:06:15,879 [trainer.py] => seed: 1993
2025-07-16 21:06:15,880 [trainer.py] => resume: False
2025-07-16 21:06:15,880 [trainer.py] => shot: 5
2025-07-16 21:06:15,880 [trainer.py] => use_simplecil: False
2025-07-16 21:06:15,880 [trainer.py] => tuned_epoch: 1
2025-07-16 21:06:15,880 [trainer.py] => init_lr: 0.01
2025-07-16 21:06:15,880 [trainer.py] => batch_size: 48
2025-07-16 21:06:15,880 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:06:15,880 [trainer.py] => min_lr: 0
2025-07-16 21:06:15,880 [trainer.py] => ffn_num: 64
2025-07-16 21:06:15,881 [trainer.py] => optimizer: sgd
2025-07-16 21:06:15,881 [trainer.py] => use_RP: True
2025-07-16 21:06:15,881 [trainer.py] => M: 10000
2025-07-16 21:06:15,881 [trainer.py] => fecam: False
2025-07-16 21:06:15,881 [trainer.py] => calibration: True
2025-07-16 21:06:15,881 [trainer.py] => knn_k: 5
2025-07-16 21:06:15,881 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:06:15,881 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:06:15,881 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:06:15,881 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:06:15,882 [trainer.py] => k_min: 3
2025-07-16 21:06:15,882 [trainer.py] => k_max: 21
2025-07-16 21:06:15,882 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:06:15,882 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:06:15,882 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 21:06:15,882 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:06:15,882 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 21:06:16,053 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:06:47,033 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 21:06:47,034 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:06:47,034 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:06:47,034 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 21:06:47,034 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:06:47,035 [trainer.py] => All params: 87578880
2025-07-16 21:06:47,036 [trainer.py] => Trainable params: 1780224
2025-07-16 21:06:58,286 [ranpac.py] => Learning on 0-100
2025-07-16 21:08:50,701 [ranpac.py] => Task 0, Epoch 1/1 => Loss nan, Train_accy 0.93, Test_accy 1.01
2025-07-16 21:09:06,826 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-16 21:09:29,144 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 21:09:29,683 [ranpac.py] => [Dynamic-K] Base similarity range: [nan, nan]
2025-07-16 21:09:29,683 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 21:09:29,683 [trainer.py] => No NME accuracy.
2025-07-16 21:09:29,683 [trainer.py] => CNN: {'total': 1.04, '00-99': 1.04, 'old': 0, 'new': 1.04}
2025-07-16 21:09:29,683 [trainer.py] => CNN HM: [0.0]
2025-07-16 21:09:29,683 [trainer.py] => CNN top1 curve: [1.04]
2025-07-16 21:09:29,683 [trainer.py] => Average Accuracy (CNN): 1.04 

2025-07-16 21:09:29,684 [trainer.py] => All params: 88578881
2025-07-16 21:09:29,685 [trainer.py] => Trainable params: 1780225
2025-07-16 21:09:29,707 [ranpac.py] => Learning on 100-110
2025-07-16 21:09:32,565 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:09:33,193 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:09:33,419 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,425 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,425 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,426 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,426 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,426 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,426 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,426 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,427 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,427 [ranpac.py] => [Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5
2025-07-16 21:09:33,427 [ranpac.py] => [Dynamic-K] Computed K values: [12, 12, 12, 12, 12, 12, 12, 12, 12, 12]
2025-07-16 21:09:33,427 [ranpac.py] => [KNN] task 1, dynamic K values: [12, 12, 12, 12, 12, 12, 12, 12, 12, 12]
2025-07-16 21:09:33,587 [ranpac.py] => [KNN] task 1, weight sparsity: 0.880, distance_metric: cosine
2025-07-16 21:09:33,588 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:11:09,668 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 21:11:09,668 [trainer.py] => prefix: emergency_fix
2025-07-16 21:11:09,669 [trainer.py] => dataset: cub
2025-07-16 21:11:09,669 [trainer.py] => memory_size: 0
2025-07-16 21:11:09,669 [trainer.py] => shuffle: True
2025-07-16 21:11:09,669 [trainer.py] => init_cls: 100
2025-07-16 21:11:09,669 [trainer.py] => increment: 10
2025-07-16 21:11:09,669 [trainer.py] => model_name: ranpac
2025-07-16 21:11:09,669 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:11:09,669 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:11:09,670 [trainer.py] => seed: 1993
2025-07-16 21:11:09,670 [trainer.py] => resume: False
2025-07-16 21:11:09,670 [trainer.py] => shot: 5
2025-07-16 21:11:09,670 [trainer.py] => use_simplecil: False
2025-07-16 21:11:09,670 [trainer.py] => tuned_epoch: 1
2025-07-16 21:11:09,670 [trainer.py] => init_lr: 0.01
2025-07-16 21:11:09,670 [trainer.py] => batch_size: 48
2025-07-16 21:11:09,670 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:11:09,671 [trainer.py] => min_lr: 0
2025-07-16 21:11:09,671 [trainer.py] => ffn_num: 64
2025-07-16 21:11:09,671 [trainer.py] => optimizer: sgd
2025-07-16 21:11:09,671 [trainer.py] => use_RP: True
2025-07-16 21:11:09,671 [trainer.py] => M: 10000
2025-07-16 21:11:09,671 [trainer.py] => fecam: False
2025-07-16 21:11:09,671 [trainer.py] => calibration: True
2025-07-16 21:11:09,671 [trainer.py] => knn_k: 5
2025-07-16 21:11:09,672 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:11:09,672 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:11:09,672 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:11:09,672 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:11:09,672 [trainer.py] => k_min: 3
2025-07-16 21:11:09,672 [trainer.py] => k_max: 21
2025-07-16 21:11:09,672 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:11:09,672 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:11:09,673 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 21:11:09,673 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:11:09,673 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 21:11:09,786 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:11:42,529 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 21:11:42,529 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:11:42,529 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:11:42,529 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 21:11:42,529 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:11:42,530 [trainer.py] => All params: 87578880
2025-07-16 21:11:42,531 [trainer.py] => Trainable params: 1780224
2025-07-16 21:11:44,049 [ranpac.py] => Learning on 0-100
2025-07-16 21:12:33,967 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.591, Train_accy 3.27, Test_accy 8.56
2025-07-16 21:12:52,592 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-16 21:13:18,532 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 21:13:19,074 [ranpac.py] => [Dynamic-K] Base similarity range: [7.0715, 10.8340]
2025-07-16 21:13:19,074 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 21:13:19,074 [trainer.py] => No NME accuracy.
2025-07-16 21:13:19,074 [trainer.py] => CNN: {'total': 90.61, '00-99': 90.61, 'old': 0, 'new': 90.61}
2025-07-16 21:13:19,074 [trainer.py] => CNN HM: [0.0]
2025-07-16 21:13:19,075 [trainer.py] => CNN top1 curve: [90.61]
2025-07-16 21:13:19,075 [trainer.py] => Average Accuracy (CNN): 90.61 

2025-07-16 21:13:19,076 [trainer.py] => All params: 88578881
2025-07-16 21:13:19,076 [trainer.py] => Trainable params: 1780225
2025-07-16 21:13:19,090 [ranpac.py] => Learning on 100-110
2025-07-16 21:13:22,203 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:13:22,232 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:13:22,234 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 6, 15, 13, 10, 6]
2025-07-16 21:13:22,234 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 6, 15, 13, 10, 6]
2025-07-16 21:13:22,235 [ranpac.py] => [KNN] task 1, weight sparsity: 0.889, distance_metric: cosine
2025-07-16 21:13:22,235 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:13:27,402 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:13:47,045 [trainer.py] => No NME accuracy.
2025-07-16 21:13:47,045 [trainer.py] => CNN: {'total': 89.15, '00-99': 89.67, '100-109': 84.12, 'old': 89.67, 'new': 84.12}
2025-07-16 21:13:47,046 [trainer.py] => CNN HM: [0.0, 86.806]
2025-07-16 21:13:47,046 [trainer.py] => CNN top1 curve: [90.61, 89.15]
2025-07-16 21:13:47,046 [trainer.py] => Average Accuracy (CNN): 89.88 

2025-07-16 21:13:47,047 [trainer.py] => All params: 88678881
2025-07-16 21:13:47,048 [trainer.py] => Trainable params: 2880225
2025-07-16 21:13:47,060 [ranpac.py] => Learning on 110-120
2025-07-16 21:27:00,495 [trainer.py] => config: ./exps/emergency_fix.json
2025-07-16 21:27:00,496 [trainer.py] => prefix: emergency_fix
2025-07-16 21:27:00,496 [trainer.py] => dataset: cub
2025-07-16 21:27:00,496 [trainer.py] => memory_size: 0
2025-07-16 21:27:00,497 [trainer.py] => shuffle: True
2025-07-16 21:27:00,497 [trainer.py] => init_cls: 100
2025-07-16 21:27:00,497 [trainer.py] => increment: 10
2025-07-16 21:27:00,497 [trainer.py] => model_name: ranpac
2025-07-16 21:27:00,497 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:27:00,497 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:27:00,497 [trainer.py] => seed: 1993
2025-07-16 21:27:00,497 [trainer.py] => resume: False
2025-07-16 21:27:00,498 [trainer.py] => shot: 5
2025-07-16 21:27:00,498 [trainer.py] => use_simplecil: False
2025-07-16 21:27:00,498 [trainer.py] => tuned_epoch: 1
2025-07-16 21:27:00,498 [trainer.py] => init_lr: 0.01
2025-07-16 21:27:00,498 [trainer.py] => batch_size: 48
2025-07-16 21:27:00,498 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:27:00,498 [trainer.py] => min_lr: 0
2025-07-16 21:27:00,498 [trainer.py] => ffn_num: 64
2025-07-16 21:27:00,499 [trainer.py] => optimizer: sgd
2025-07-16 21:27:00,499 [trainer.py] => use_RP: True
2025-07-16 21:27:00,499 [trainer.py] => M: 10000
2025-07-16 21:27:00,499 [trainer.py] => fecam: False
2025-07-16 21:27:00,499 [trainer.py] => calibration: True
2025-07-16 21:27:00,499 [trainer.py] => knn_k: 5
2025-07-16 21:27:00,499 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:27:00,499 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:27:00,499 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:27:00,500 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:27:00,500 [trainer.py] => k_min: 3
2025-07-16 21:27:00,500 [trainer.py] => k_max: 21
2025-07-16 21:27:00,500 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:27:00,500 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:27:00,500 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 21:27:00,500 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:27:00,501 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 21:27:00,661 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:27:26,775 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 21:27:26,776 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:27:26,776 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:27:26,776 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 21:27:26,776 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:27:26,777 [trainer.py] => All params: 87578880
2025-07-16 21:27:26,777 [trainer.py] => Trainable params: 1780224
2025-07-16 21:27:28,359 [ranpac.py] => Learning on 0-100
2025-07-16 21:28:50,418 [ranpac.py] => Task 0, Epoch 1/1 => Loss 16.592, Disentangle_Loss 120.000, Train_accy 2.94, Test_accy 8.49
2025-07-16 21:29:08,583 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-16 21:29:30,989 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 21:29:31,527 [ranpac.py] => [Dynamic-K] Base similarity range: [7.0738, 10.8347]
2025-07-16 21:29:31,527 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 21:29:31,527 [trainer.py] => No NME accuracy.
2025-07-16 21:29:31,527 [trainer.py] => CNN: {'total': 90.61, '00-99': 90.61, 'old': 0, 'new': 90.61}
2025-07-16 21:29:31,527 [trainer.py] => CNN HM: [0.0]
2025-07-16 21:29:31,527 [trainer.py] => CNN top1 curve: [90.61]
2025-07-16 21:29:31,527 [trainer.py] => Average Accuracy (CNN): 90.61 

2025-07-16 21:29:31,528 [trainer.py] => All params: 88578881
2025-07-16 21:29:31,529 [trainer.py] => Trainable params: 1780225
2025-07-16 21:29:31,546 [ranpac.py] => Learning on 100-110
2025-07-16 21:29:34,956 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:29:35,507 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:29:35,508 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 6, 15, 13, 10, 6]
2025-07-16 21:29:35,508 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 6, 15, 13, 10, 6]
2025-07-16 21:29:35,509 [ranpac.py] => [KNN] task 1, weight sparsity: 0.889, distance_metric: cosine
2025-07-16 21:29:35,509 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:29:38,627 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:29:56,999 [trainer.py] => No NME accuracy.
2025-07-16 21:29:57,000 [trainer.py] => CNN: {'total': 89.22, '00-99': 89.74, '100-109': 84.12, 'old': 89.74, 'new': 84.12}
2025-07-16 21:29:57,000 [trainer.py] => CNN HM: [0.0, 86.839]
2025-07-16 21:29:57,000 [trainer.py] => CNN top1 curve: [90.61, 89.22]
2025-07-16 21:29:57,000 [trainer.py] => Average Accuracy (CNN): 89.91499999999999 

2025-07-16 21:29:57,002 [trainer.py] => All params: 88678881
2025-07-16 21:29:57,003 [trainer.py] => Trainable params: 2880225
2025-07-16 21:29:57,021 [ranpac.py] => Learning on 110-120
2025-07-16 21:30:00,422 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:30:01,051 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:30:01,053 [ranpac.py] => [Dynamic-K] Computed K values: [4, 12, 9, 9, 17, 17, 11, 5, 15, 16]
2025-07-16 21:30:01,053 [ranpac.py] => [KNN] task 2, dynamic K values: [4, 12, 9, 9, 17, 17, 11, 5, 15, 16]
2025-07-16 21:30:01,054 [ranpac.py] => [KNN] task 2, weight sparsity: 0.885, distance_metric: cosine
2025-07-16 21:30:01,054 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:30:05,617 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:30:24,900 [trainer.py] => No NME accuracy.
2025-07-16 21:30:24,900 [trainer.py] => CNN: {'total': 87.68, '00-99': 90.05, '100-109': 84.46, '110-119': 66.9, 'old': 89.53, 'new': 66.9}
2025-07-16 21:30:24,901 [trainer.py] => CNN HM: [0.0, 86.839, 76.578]
2025-07-16 21:30:24,901 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68]
2025-07-16 21:30:24,901 [trainer.py] => Average Accuracy (CNN): 89.17 

2025-07-16 21:30:24,903 [trainer.py] => All params: 88778881
2025-07-16 21:30:24,904 [trainer.py] => Trainable params: 2980225
2025-07-16 21:30:24,917 [ranpac.py] => Learning on 120-130
2025-07-16 21:30:28,067 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:30:28,249 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:30:28,251 [ranpac.py] => [Dynamic-K] Computed K values: [10, 10, 10, 16, 6, 6, 6, 7, 8, 8]
2025-07-16 21:30:28,251 [ranpac.py] => [KNN] task 3, dynamic K values: [10, 10, 10, 16, 6, 6, 6, 7, 8, 8]
2025-07-16 21:30:28,252 [ranpac.py] => [KNN] task 3, weight sparsity: 0.913, distance_metric: cosine
2025-07-16 21:30:28,252 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:30:31,855 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:30:53,389 [trainer.py] => No NME accuracy.
2025-07-16 21:30:53,389 [trainer.py] => CNN: {'total': 85.57, '00-99': 89.25, '100-109': 84.46, '110-119': 66.55, '120-129': 68.62, 'old': 86.98, 'new': 68.62}
2025-07-16 21:30:53,390 [trainer.py] => CNN HM: [0.0, 86.839, 76.578, 76.717]
2025-07-16 21:30:53,390 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68, 85.57]
2025-07-16 21:30:53,390 [trainer.py] => Average Accuracy (CNN): 88.27 

2025-07-16 21:30:53,392 [trainer.py] => All params: 88878881
2025-07-16 21:30:53,393 [trainer.py] => Trainable params: 3080225
2025-07-16 21:30:53,409 [ranpac.py] => Learning on 130-140
2025-07-16 21:30:58,009 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:30:58,664 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:30:58,666 [ranpac.py] => [Dynamic-K] Computed K values: [9, 15, 10, 11, 14, 6, 9, 8, 11, 10]
2025-07-16 21:30:58,666 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 15, 10, 11, 14, 6, 9, 8, 11, 10]
2025-07-16 21:30:58,666 [ranpac.py] => [KNN] task 4, weight sparsity: 0.897, distance_metric: cosine
2025-07-16 21:30:58,666 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:31:01,571 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:31:26,096 [trainer.py] => No NME accuracy.
2025-07-16 21:31:26,096 [trainer.py] => CNN: {'total': 84.85, '00-99': 89.08, '100-109': 83.78, '110-119': 67.25, '120-129': 65.86, '130-139': 80.0, 'old': 85.22, 'new': 80.0}
2025-07-16 21:31:26,096 [trainer.py] => CNN HM: [0.0, 86.839, 76.578, 76.717, 82.528]
2025-07-16 21:31:26,096 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68, 85.57, 84.85]
2025-07-16 21:31:26,096 [trainer.py] => Average Accuracy (CNN): 87.58599999999998 

2025-07-16 21:31:26,098 [trainer.py] => All params: 88978881
2025-07-16 21:31:26,099 [trainer.py] => Trainable params: 3180225
2025-07-16 21:31:26,122 [ranpac.py] => Learning on 140-150
2025-07-16 21:31:30,797 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:31:30,895 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:31:30,896 [ranpac.py] => [Dynamic-K] Computed K values: [11, 14, 12, 7, 8, 12, 11, 4, 8, 9]
2025-07-16 21:31:30,896 [ranpac.py] => [KNN] task 5, dynamic K values: [11, 14, 12, 7, 8, 12, 11, 4, 8, 9]
2025-07-16 21:31:30,897 [ranpac.py] => [KNN] task 5, weight sparsity: 0.904, distance_metric: cosine
2025-07-16 21:31:30,897 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:31:33,731 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:31:58,634 [trainer.py] => No NME accuracy.
2025-07-16 21:31:58,635 [trainer.py] => CNN: {'total': 83.11, '00-99': 88.35, '100-109': 84.8, '110-119': 65.85, '120-129': 65.17, '130-139': 80.34, '140-149': 66.19, 'old': 84.28, 'new': 66.19}
2025-07-16 21:31:58,636 [trainer.py] => CNN HM: [0.0, 86.839, 76.578, 76.717, 82.528, 74.148]
2025-07-16 21:31:58,636 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68, 85.57, 84.85, 83.11]
2025-07-16 21:31:58,637 [trainer.py] => Average Accuracy (CNN): 86.83999999999999 

2025-07-16 21:31:58,639 [trainer.py] => All params: 89078881
2025-07-16 21:31:58,641 [trainer.py] => Trainable params: 3280225
2025-07-16 21:31:58,657 [ranpac.py] => Learning on 150-160
2025-07-16 21:32:01,838 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:32:02,023 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:32:02,029 [ranpac.py] => [Dynamic-K] Computed K values: [8, 12, 8, 7, 9, 9, 8, 7, 18, 9]
2025-07-16 21:32:02,031 [ranpac.py] => [KNN] task 6, dynamic K values: [8, 12, 8, 7, 9, 9, 8, 7, 18, 9]
2025-07-16 21:32:02,032 [ranpac.py] => [KNN] task 6, weight sparsity: 0.905, distance_metric: cosine
2025-07-16 21:32:02,032 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:32:05,674 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:32:34,956 [trainer.py] => No NME accuracy.
2025-07-16 21:32:34,957 [trainer.py] => CNN: {'total': 82.62, '00-99': 88.28, '100-109': 83.78, '110-119': 65.14, '120-129': 64.83, '130-139': 80.34, '140-149': 66.19, '150-159': 78.08, 'old': 82.93, 'new': 78.08}
2025-07-16 21:32:34,957 [trainer.py] => CNN HM: [0.0, 86.839, 76.578, 76.717, 82.528, 74.148, 80.432]
2025-07-16 21:32:34,957 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68, 85.57, 84.85, 83.11, 82.62]
2025-07-16 21:32:34,957 [trainer.py] => Average Accuracy (CNN): 86.23714285714286 

2025-07-16 21:32:34,959 [trainer.py] => All params: 89178881
2025-07-16 21:32:34,960 [trainer.py] => Trainable params: 3380225
2025-07-16 21:32:34,978 [ranpac.py] => Learning on 160-170
2025-07-16 21:32:40,653 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:32:40,785 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:32:40,786 [ranpac.py] => [Dynamic-K] Computed K values: [15, 13, 13, 6, 12, 8, 10, 6, 11, 7]
2025-07-16 21:32:40,786 [ranpac.py] => [KNN] task 7, dynamic K values: [15, 13, 13, 6, 12, 8, 10, 6, 11, 7]
2025-07-16 21:32:40,787 [ranpac.py] => [KNN] task 7, weight sparsity: 0.899, distance_metric: cosine
2025-07-16 21:32:40,787 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:32:44,231 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:33:16,852 [trainer.py] => No NME accuracy.
2025-07-16 21:33:16,853 [trainer.py] => CNN: {'total': 81.7, '00-99': 88.15, '100-109': 83.78, '110-119': 64.44, '120-129': 65.52, '130-139': 78.62, '140-149': 60.79, '150-159': 78.08, '160-169': 75.5, 'old': 82.1, 'new': 75.5}
2025-07-16 21:33:16,853 [trainer.py] => CNN HM: [0.0, 86.839, 76.578, 76.717, 82.528, 74.148, 80.432, 78.662]
2025-07-16 21:33:16,853 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68, 85.57, 84.85, 83.11, 82.62, 81.7]
2025-07-16 21:33:16,853 [trainer.py] => Average Accuracy (CNN): 85.67 

2025-07-16 21:33:16,855 [trainer.py] => All params: 89278881
2025-07-16 21:33:16,856 [trainer.py] => Trainable params: 3480225
2025-07-16 21:33:16,873 [ranpac.py] => Learning on 170-180
2025-07-16 21:33:22,270 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:33:22,480 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:33:22,482 [ranpac.py] => [Dynamic-K] Computed K values: [13, 8, 10, 7, 9, 7, 4, 10, 15, 6]
2025-07-16 21:33:22,482 [ranpac.py] => [KNN] task 8, dynamic K values: [13, 8, 10, 7, 9, 7, 4, 10, 15, 6]
2025-07-16 21:33:22,482 [ranpac.py] => [KNN] task 8, weight sparsity: 0.911, distance_metric: cosine
2025-07-16 21:33:22,482 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:33:25,530 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:33:57,267 [trainer.py] => No NME accuracy.
2025-07-16 21:33:57,267 [trainer.py] => CNN: {'total': 81.17, '00-99': 87.94, '100-109': 83.45, '110-119': 65.49, '120-129': 65.52, '130-139': 77.93, '140-149': 58.63, '150-159': 77.05, '160-169': 75.5, '170-179': 77.52, 'old': 81.4, 'new': 77.52}
2025-07-16 21:33:57,268 [trainer.py] => CNN HM: [0.0, 86.839, 76.578, 76.717, 82.528, 74.148, 80.432, 78.662, 79.413]
2025-07-16 21:33:57,268 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68, 85.57, 84.85, 83.11, 82.62, 81.7, 81.17]
2025-07-16 21:33:57,268 [trainer.py] => Average Accuracy (CNN): 85.17 

2025-07-16 21:33:57,269 [trainer.py] => All params: 89378881
2025-07-16 21:33:57,270 [trainer.py] => Trainable params: 3580225
2025-07-16 21:33:57,289 [ranpac.py] => Learning on 180-190
2025-07-16 21:34:00,240 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:34:01,210 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:34:01,211 [ranpac.py] => [Dynamic-K] Computed K values: [14, 13, 9, 11, 8, 9, 18, 13, 15, 9]
2025-07-16 21:34:01,211 [ranpac.py] => [KNN] task 9, dynamic K values: [14, 13, 9, 11, 8, 9, 18, 13, 15, 9]
2025-07-16 21:34:01,212 [ranpac.py] => [KNN] task 9, weight sparsity: 0.881, distance_metric: cosine
2025-07-16 21:34:01,212 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:34:04,981 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:34:38,545 [trainer.py] => No NME accuracy.
2025-07-16 21:34:38,545 [trainer.py] => CNN: {'total': 80.47, '00-99': 87.76, '100-109': 84.12, '110-119': 65.14, '120-129': 64.14, '130-139': 75.17, '140-149': 58.27, '150-159': 77.74, '160-169': 75.5, '170-179': 78.19, '180-189': 72.13, 'old': 80.92, 'new': 72.13}
2025-07-16 21:34:38,545 [trainer.py] => CNN HM: [0.0, 86.839, 76.578, 76.717, 82.528, 74.148, 80.432, 78.662, 79.413, 76.273]
2025-07-16 21:34:38,545 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68, 85.57, 84.85, 83.11, 82.62, 81.7, 81.17, 80.47]
2025-07-16 21:34:38,545 [trainer.py] => Average Accuracy (CNN): 84.7 

2025-07-16 21:34:38,546 [trainer.py] => All params: 89478881
2025-07-16 21:34:38,547 [trainer.py] => Trainable params: 3680225
2025-07-16 21:34:38,566 [ranpac.py] => Learning on 190-200
2025-07-16 21:34:41,034 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 21:34:41,119 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 21:34:41,120 [ranpac.py] => [Dynamic-K] Computed K values: [6, 10, 11, 7, 10, 9, 8, 6, 12, 8]
2025-07-16 21:34:41,120 [ranpac.py] => [KNN] task 10, dynamic K values: [6, 10, 11, 7, 10, 9, 8, 6, 12, 8]
2025-07-16 21:34:41,121 [ranpac.py] => [KNN] task 10, weight sparsity: 0.913, distance_metric: cosine
2025-07-16 21:34:41,121 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 21:34:44,944 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 21:35:18,761 [trainer.py] => No NME accuracy.
2025-07-16 21:35:18,761 [trainer.py] => CNN: {'total': 79.62, '00-99': 87.28, '100-109': 85.47, '110-119': 65.14, '120-129': 65.52, '130-139': 75.86, '140-149': 58.27, '150-159': 77.74, '160-169': 75.17, '170-179': 77.18, '180-189': 72.13, '190-199': 66.55, 'old': 80.32, 'new': 66.55}
2025-07-16 21:35:18,761 [trainer.py] => CNN HM: [0.0, 86.839, 76.578, 76.717, 82.528, 74.148, 80.432, 78.662, 79.413, 76.273, 72.789]
2025-07-16 21:35:18,761 [trainer.py] => CNN top1 curve: [90.61, 89.22, 87.68, 85.57, 84.85, 83.11, 82.62, 81.7, 81.17, 80.47, 79.62]
2025-07-16 21:35:18,761 [trainer.py] => Average Accuracy (CNN): 84.23818181818181 

2025-07-16 21:35:18,763 [trainer.py] => Forgetting (CNN): 2.2619999999999996
