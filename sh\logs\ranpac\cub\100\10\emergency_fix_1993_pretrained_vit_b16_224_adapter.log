2025-07-16 21:39:07,296 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/emergency_fix.json
2025-07-16 21:39:07,298 [trainer.py] => prefix: emergency_fix
2025-07-16 21:39:07,298 [trainer.py] => dataset: cub
2025-07-16 21:39:07,298 [trainer.py] => memory_size: 0
2025-07-16 21:39:07,298 [trainer.py] => shuffle: True
2025-07-16 21:39:07,299 [trainer.py] => init_cls: 100
2025-07-16 21:39:07,299 [trainer.py] => increment: 10
2025-07-16 21:39:07,299 [trainer.py] => model_name: ranpac
2025-07-16 21:39:07,299 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:39:07,299 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:39:07,299 [trainer.py] => seed: 1993
2025-07-16 21:39:07,299 [trainer.py] => resume: False
2025-07-16 21:39:07,299 [trainer.py] => shot: 5
2025-07-16 21:39:07,299 [trainer.py] => use_simplecil: False
2025-07-16 21:39:07,299 [trainer.py] => tuned_epoch: 40
2025-07-16 21:39:07,300 [trainer.py] => init_lr: 0.01
2025-07-16 21:39:07,300 [trainer.py] => batch_size: 48
2025-07-16 21:39:07,300 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:39:07,300 [trainer.py] => min_lr: 0
2025-07-16 21:39:07,300 [trainer.py] => ffn_num: 64
2025-07-16 21:39:07,300 [trainer.py] => optimizer: sgd
2025-07-16 21:39:07,300 [trainer.py] => use_RP: True
2025-07-16 21:39:07,300 [trainer.py] => M: 10000
2025-07-16 21:39:07,300 [trainer.py] => fecam: False
2025-07-16 21:39:07,300 [trainer.py] => calibration: True
2025-07-16 21:39:07,300 [trainer.py] => knn_k: 5
2025-07-16 21:39:07,300 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:39:07,301 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:39:07,301 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:39:07,301 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:39:07,301 [trainer.py] => k_min: 3
2025-07-16 21:39:07,301 [trainer.py] => k_max: 21
2025-07-16 21:39:07,301 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:39:07,301 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:39:07,301 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 21:39:07,301 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:39:07,301 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 21:39:07,545 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:39:43,391 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:39:43,393 [trainer.py] => All params: 87578880
2025-07-16 21:39:43,393 [trainer.py] => Trainable params: 1780224
2025-07-16 21:39:54,540 [ranpac.py] => Learning on 0-100
2025-07-16 22:12:13,455 [ranpac.py] => Task 0, Epoch 40/40 => Loss 18.885, Disentangle_Loss 184.684, Train_accy 92.23, Test_accy 92.48
2025-07-16 22:12:36,919 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([2997, 384]), variation features: torch.Size([2997, 384])
2025-07-16 22:13:09,905 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-16 22:13:10,347 [ranpac.py] => [Dynamic-K] Base similarity range: [0.8417, 2.1442]
2025-07-16 22:13:10,349 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-16 22:13:10,351 [trainer.py] => No NME accuracy.
2025-07-16 22:13:10,352 [trainer.py] => CNN: {'total': 92.31, '00-99': 92.31, 'old': 0, 'new': 92.31}
2025-07-16 22:13:10,354 [trainer.py] => CNN HM: [0.0]
2025-07-16 22:13:10,355 [trainer.py] => CNN top1 curve: [92.31]
2025-07-16 22:13:10,356 [trainer.py] => Average Accuracy (CNN): 92.31 

2025-07-16 22:13:10,358 [trainer.py] => All params: 88578881
2025-07-16 22:13:10,360 [trainer.py] => Trainable params: 1780225
2025-07-16 22:13:10,523 [ranpac.py] => Learning on 100-110
2025-07-16 22:13:14,116 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:13:14,315 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:13:14,318 [ranpac.py] => [Dynamic-K] Computed K values: [17, 17, 11, 13, 6, 9, 12, 15, 13, 5]
2025-07-16 22:13:14,319 [ranpac.py] => [KNN] task 1, dynamic K values: [17, 17, 11, 13, 6, 9, 12, 15, 13, 5]
2025-07-16 22:13:17,260 [ranpac.py] => [KNN] task 1, weight sparsity: 0.882, distance_metric: cosine
2025-07-16 22:13:17,262 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:13:20,547 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:13:46,816 [trainer.py] => No NME accuracy.
2025-07-16 22:13:46,819 [trainer.py] => CNN: {'total': 91.48, '00-99': 91.68, '100-109': 89.53, 'old': 91.68, 'new': 89.53}
2025-07-16 22:13:46,819 [trainer.py] => CNN HM: [0.0, 90.592]
2025-07-16 22:13:46,820 [trainer.py] => CNN top1 curve: [92.31, 91.48]
2025-07-16 22:13:46,821 [trainer.py] => Average Accuracy (CNN): 91.89500000000001 

2025-07-16 22:13:46,823 [trainer.py] => All params: 88678881
2025-07-16 22:13:46,825 [trainer.py] => Trainable params: 2880225
2025-07-16 22:13:46,840 [ranpac.py] => Learning on 110-120
2025-07-16 22:13:50,315 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:13:50,334 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:13:50,336 [ranpac.py] => [Dynamic-K] Computed K values: [6, 14, 14, 9, 17, 14, 12, 7, 17, 13]
2025-07-16 22:13:50,336 [ranpac.py] => [KNN] task 2, dynamic K values: [6, 14, 14, 9, 17, 14, 12, 7, 17, 13]
2025-07-16 22:13:50,337 [ranpac.py] => [KNN] task 2, weight sparsity: 0.877, distance_metric: cosine
2025-07-16 22:13:50,337 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:13:52,084 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:14:18,648 [trainer.py] => No NME accuracy.
2025-07-16 22:14:18,650 [trainer.py] => CNN: {'total': 90.19, '00-99': 91.61, '100-109': 89.53, '110-119': 76.41, 'old': 91.42, 'new': 76.41}
2025-07-16 22:14:18,651 [trainer.py] => CNN HM: [0.0, 90.592, 83.244]
2025-07-16 22:14:18,651 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19]
2025-07-16 22:14:18,652 [trainer.py] => Average Accuracy (CNN): 91.32666666666667 

2025-07-16 22:14:18,653 [trainer.py] => All params: 88778881
2025-07-16 22:14:18,654 [trainer.py] => Trainable params: 2980225
2025-07-16 22:14:18,674 [ranpac.py] => Learning on 120-130
2025-07-16 22:14:22,143 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:14:22,156 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:14:22,159 [ranpac.py] => [Dynamic-K] Computed K values: [12, 11, 14, 13, 10, 6, 4, 10, 7, 10]
2025-07-16 22:14:22,161 [ranpac.py] => [KNN] task 3, dynamic K values: [12, 11, 14, 13, 10, 6, 4, 10, 7, 10]
2025-07-16 22:14:22,163 [ranpac.py] => [KNN] task 3, weight sparsity: 0.903, distance_metric: cosine
2025-07-16 22:14:22,164 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:14:23,720 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:14:48,360 [trainer.py] => No NME accuracy.
2025-07-16 22:14:48,362 [trainer.py] => CNN: {'total': 88.84, '00-99': 91.33, '100-109': 90.88, '110-119': 76.06, '120-129': 74.48, 'old': 90.04, 'new': 74.48}
2025-07-16 22:14:48,362 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524]
2025-07-16 22:14:48,363 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84]
2025-07-16 22:14:48,363 [trainer.py] => Average Accuracy (CNN): 90.70500000000001 

2025-07-16 22:14:48,365 [trainer.py] => All params: 88878881
2025-07-16 22:14:48,367 [trainer.py] => Trainable params: 3080225
2025-07-16 22:14:48,381 [ranpac.py] => Learning on 130-140
2025-07-16 22:14:52,251 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:14:52,267 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:14:52,270 [ranpac.py] => [Dynamic-K] Computed K values: [14, 11, 16, 11, 20, 5, 14, 11, 12, 12]
2025-07-16 22:14:52,271 [ranpac.py] => [KNN] task 4, dynamic K values: [14, 11, 16, 11, 20, 5, 14, 11, 12, 12]
2025-07-16 22:14:52,273 [ranpac.py] => [KNN] task 4, weight sparsity: 0.874, distance_metric: cosine
2025-07-16 22:14:52,274 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:14:54,027 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:15:19,779 [trainer.py] => No NME accuracy.
2025-07-16 22:15:19,781 [trainer.py] => CNN: {'total': 88.28, '00-99': 91.16, '100-109': 90.54, '110-119': 76.06, '120-129': 72.07, '130-139': 85.52, 'old': 88.5, 'new': 85.52}
2025-07-16 22:15:19,782 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984]
2025-07-16 22:15:19,783 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28]
2025-07-16 22:15:19,783 [trainer.py] => Average Accuracy (CNN): 90.22 

2025-07-16 22:15:19,785 [trainer.py] => All params: 88978881
2025-07-16 22:15:19,786 [trainer.py] => Trainable params: 3180225
2025-07-16 22:15:19,803 [ranpac.py] => Learning on 140-150
2025-07-16 22:15:22,714 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:15:22,732 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:15:22,735 [ranpac.py] => [Dynamic-K] Computed K values: [9, 16, 13, 7, 12, 15, 16, 5, 6, 16]
2025-07-16 22:15:22,736 [ranpac.py] => [KNN] task 5, dynamic K values: [9, 16, 13, 7, 12, 15, 16, 5, 6, 16]
2025-07-16 22:15:22,737 [ranpac.py] => [KNN] task 5, weight sparsity: 0.885, distance_metric: cosine
2025-07-16 22:15:22,738 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:15:24,554 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:15:55,416 [trainer.py] => No NME accuracy.
2025-07-16 22:15:55,419 [trainer.py] => CNN: {'total': 86.63, '00-99': 90.54, '100-109': 90.88, '110-119': 73.24, '120-129': 70.69, '130-139': 85.86, '140-149': 72.66, 'old': 87.59, 'new': 72.66}
2025-07-16 22:15:55,419 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43]
2025-07-16 22:15:55,419 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63]
2025-07-16 22:15:55,420 [trainer.py] => Average Accuracy (CNN): 89.62166666666667 

2025-07-16 22:15:55,421 [trainer.py] => All params: 89078881
2025-07-16 22:15:55,422 [trainer.py] => Trainable params: 3280225
2025-07-16 22:15:55,443 [ranpac.py] => Learning on 150-160
2025-07-16 22:15:59,183 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:15:59,199 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:15:59,202 [ranpac.py] => [Dynamic-K] Computed K values: [11, 15, 11, 8, 13, 8, 7, 8, 21, 13]
2025-07-16 22:15:59,204 [ranpac.py] => [KNN] task 6, dynamic K values: [11, 15, 11, 8, 13, 8, 7, 8, 21, 13]
2025-07-16 22:15:59,208 [ranpac.py] => [KNN] task 6, weight sparsity: 0.885, distance_metric: cosine
2025-07-16 22:15:59,209 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:16:00,798 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:16:35,097 [trainer.py] => No NME accuracy.
2025-07-16 22:16:35,099 [trainer.py] => CNN: {'total': 86.26, '00-99': 90.64, '100-109': 88.51, '110-119': 73.94, '120-129': 70.34, '130-139': 85.86, '140-149': 71.94, '150-159': 82.53, 'old': 86.51, 'new': 82.53}
2025-07-16 22:16:35,100 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473]
2025-07-16 22:16:35,101 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26]
2025-07-16 22:16:35,101 [trainer.py] => Average Accuracy (CNN): 89.14142857142858 

2025-07-16 22:16:35,104 [trainer.py] => All params: 89178881
2025-07-16 22:16:35,105 [trainer.py] => Trainable params: 3380225
2025-07-16 22:16:35,123 [ranpac.py] => Learning on 160-170
2025-07-16 22:16:38,732 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:16:38,753 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:16:38,755 [ranpac.py] => [Dynamic-K] Computed K values: [15, 18, 16, 5, 16, 11, 10, 6, 12, 11]
2025-07-16 22:16:38,757 [ranpac.py] => [KNN] task 7, dynamic K values: [15, 18, 16, 5, 16, 11, 10, 6, 12, 11]
2025-07-16 22:16:38,758 [ranpac.py] => [KNN] task 7, weight sparsity: 0.880, distance_metric: cosine
2025-07-16 22:16:38,759 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:16:40,345 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:17:10,868 [trainer.py] => No NME accuracy.
2025-07-16 22:17:10,870 [trainer.py] => CNN: {'total': 85.77, '00-99': 90.4, '100-109': 88.51, '110-119': 73.24, '120-129': 71.03, '130-139': 85.52, '140-149': 67.63, '150-159': 83.56, '160-169': 83.89, 'old': 85.89, 'new': 83.89}
2025-07-16 22:17:10,870 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473, 84.878]
2025-07-16 22:17:10,871 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26, 85.77]
2025-07-16 22:17:10,871 [trainer.py] => Average Accuracy (CNN): 88.72 

2025-07-16 22:17:10,872 [trainer.py] => All params: 89278881
2025-07-16 22:17:10,873 [trainer.py] => Trainable params: 3480225
2025-07-16 22:17:10,891 [ranpac.py] => Learning on 170-180
2025-07-16 22:17:13,081 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:17:13,129 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:17:13,132 [ranpac.py] => [Dynamic-K] Computed K values: [17, 7, 12, 10, 15, 7, 6, 9, 17, 8]
2025-07-16 22:17:13,133 [ranpac.py] => [KNN] task 8, dynamic K values: [17, 7, 12, 10, 15, 7, 6, 9, 17, 8]
2025-07-16 22:17:13,136 [ranpac.py] => [KNN] task 8, weight sparsity: 0.892, distance_metric: cosine
2025-07-16 22:17:13,138 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:17:14,875 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:17:54,772 [trainer.py] => No NME accuracy.
2025-07-16 22:17:54,774 [trainer.py] => CNN: {'total': 85.15, '00-99': 89.84, '100-109': 88.51, '110-119': 72.54, '120-129': 71.03, '130-139': 86.21, '140-149': 66.91, '150-159': 82.19, '160-169': 83.56, '170-179': 82.55, 'old': 85.3, 'new': 82.55}
2025-07-16 22:17:54,775 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473, 84.878, 83.902]
2025-07-16 22:17:54,775 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26, 85.77, 85.15]
2025-07-16 22:17:54,776 [trainer.py] => Average Accuracy (CNN): 88.32333333333332 

2025-07-16 22:17:54,778 [trainer.py] => All params: 89378881
2025-07-16 22:17:54,779 [trainer.py] => Trainable params: 3580225
2025-07-16 22:17:54,796 [ranpac.py] => Learning on 180-190
2025-07-16 22:17:58,488 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:17:58,503 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:17:58,506 [ranpac.py] => [Dynamic-K] Computed K values: [12, 12, 8, 14, 11, 9, 14, 15, 18, 11]
2025-07-16 22:17:58,508 [ranpac.py] => [KNN] task 9, dynamic K values: [12, 12, 8, 14, 11, 9, 14, 15, 18, 11]
2025-07-16 22:17:58,510 [ranpac.py] => [KNN] task 9, weight sparsity: 0.876, distance_metric: cosine
2025-07-16 22:17:58,511 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:18:00,332 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:18:40,261 [trainer.py] => No NME accuracy.
2025-07-16 22:18:40,264 [trainer.py] => CNN: {'total': 84.56, '00-99': 89.77, '100-109': 89.19, '110-119': 72.18, '120-129': 70.34, '130-139': 83.79, '140-149': 66.55, '150-159': 82.53, '160-169': 83.22, '170-179': 81.88, '180-189': 78.4, 'old': 84.9, 'new': 78.4}
2025-07-16 22:18:40,264 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473, 84.878, 83.902, 81.521]
2025-07-16 22:18:40,264 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26, 85.77, 85.15, 84.56]
2025-07-16 22:18:40,265 [trainer.py] => Average Accuracy (CNN): 87.947 

2025-07-16 22:18:40,267 [trainer.py] => All params: 89478881
2025-07-16 22:18:40,268 [trainer.py] => Trainable params: 3680225
2025-07-16 22:18:40,289 [ranpac.py] => Learning on 190-200
2025-07-16 22:18:45,924 [ranpac.py] => [Disentangled Adapter] Extracted identity features: torch.Size([50, 384]), variation features: torch.Size([50, 384])
2025-07-16 22:18:45,942 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-16 22:18:45,944 [ranpac.py] => [Dynamic-K] Computed K values: [5, 14, 12, 10, 13, 7, 8, 3, 15, 8]
2025-07-16 22:18:45,945 [ranpac.py] => [KNN] task 10, dynamic K values: [5, 14, 12, 10, 13, 7, 8, 3, 15, 8]
2025-07-16 22:18:45,947 [ranpac.py] => [KNN] task 10, weight sparsity: 0.905, distance_metric: cosine
2025-07-16 22:18:45,948 [ranpac.py] => [Emergency Fix] Using original KNN weight computation
2025-07-16 22:18:47,795 [ranpac.py] => [Disentangled Covariance] Generated 8000 augmented samples
2025-07-16 22:19:27,417 [trainer.py] => No NME accuracy.
2025-07-16 22:19:27,419 [trainer.py] => CNN: {'total': 83.72, '00-99': 89.36, '100-109': 89.86, '110-119': 72.89, '120-129': 71.03, '130-139': 83.79, '140-149': 66.55, '150-159': 82.53, '160-169': 82.21, '170-179': 80.87, '180-189': 78.05, '190-199': 72.64, 'old': 84.32, 'new': 72.64}
2025-07-16 22:19:27,419 [trainer.py] => CNN HM: [0.0, 90.592, 83.244, 81.524, 86.984, 79.43, 84.473, 84.878, 83.902, 81.521, 78.045]
2025-07-16 22:19:27,419 [trainer.py] => CNN top1 curve: [92.31, 91.48, 90.19, 88.84, 88.28, 86.63, 86.26, 85.77, 85.15, 84.56, 83.72]
2025-07-16 22:19:27,420 [trainer.py] => Average Accuracy (CNN): 87.56272727272727 

2025-07-16 22:19:27,421 [trainer.py] => Forgetting (CNN): 2.4209999999999994
