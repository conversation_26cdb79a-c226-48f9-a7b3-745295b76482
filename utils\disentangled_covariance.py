"""
解耦协方差校准模块
基于解耦特征重新设计协方差矩阵计算和校准过程
"""

import torch
import torch.nn.functional as F
import numpy as np
import logging


class DisentangledCovarianceCalibrator:
    """
    解耦协方差校准器
    使用身份特征计算相似度权重，使用变化特征建模协方差矩阵
    """
    
    def __init__(self, 
                 variation_cov_weight=0.8,
                 identity_similarity_weight=1.0,
                 regularization_strength=1e-6,
                 min_samples_for_cov=5):
        """
        初始化解耦协方差校准器
        
        Args:
            variation_cov_weight: 变化特征协方差的权重
            identity_similarity_weight: 身份相似度的权重
            regularization_strength: 协方差矩阵正则化强度
            min_samples_for_cov: 计算协方差矩阵的最小样本数
        """
        self.variation_cov_weight = variation_cov_weight
        self.identity_similarity_weight = identity_similarity_weight
        self.regularization_strength = regularization_strength
        self.min_samples_for_cov = min_samples_for_cov
        
    def compute_disentangled_covariance(self, 
                                      identity_features, 
                                      variation_features, 
                                      original_features=None):
        """
        基于解耦特征计算协方差矩阵
        
        Args:
            identity_features: [n_samples, identity_dim] 身份特征
            variation_features: [n_samples, variation_dim] 变化特征
            original_features: [n_samples, original_dim] 原始特征（可选）
            
        Returns:
            covariance_matrix: 校准后的协方差矩阵
        """
        n_samples = variation_features.shape[0]
        
        if n_samples < self.min_samples_for_cov:
            logging.warning(f"[Disentangled Covariance] Insufficient samples ({n_samples}) for covariance computation, "
                          f"minimum required: {self.min_samples_for_cov}")
            # 返回单位矩阵作为默认协方差
            if original_features is not None:
                return torch.eye(original_features.shape[1]) * self.regularization_strength
            else:
                return torch.eye(variation_features.shape[1]) * self.regularization_strength
        
        # 计算变化特征的协方差矩阵
        variation_cov = torch.cov(variation_features.T)
        
        # 添加正则化
        variation_dim = variation_features.shape[1]
        variation_cov += self.regularization_strength * torch.eye(variation_dim, device=variation_cov.device)
        
        if original_features is not None:
            # 计算原始特征的协方差矩阵
            original_cov = torch.cov(original_features.T)
            original_dim = original_features.shape[1]
            original_cov += self.regularization_strength * torch.eye(original_dim, device=original_cov.device)
            
            # 如果维度不匹配，需要进行维度对齐
            if variation_dim != original_dim:
                if variation_dim < original_dim:
                    # 扩展变化协方差矩阵
                    expanded_variation_cov = torch.zeros_like(original_cov)
                    expanded_variation_cov[:variation_dim, :variation_dim] = variation_cov
                    variation_cov = expanded_variation_cov
                else:
                    # 截断变化协方差矩阵
                    variation_cov = variation_cov[:original_dim, :original_dim]
            
            # 加权组合
            combined_cov = (self.variation_cov_weight * variation_cov + 
                          (1 - self.variation_cov_weight) * original_cov)
            
            return combined_cov
        else:
            return variation_cov
    
    def compute_identity_similarity_weights(self, 
                                          new_identity_protos, 
                                          base_identity_protos,
                                          similarity_metric="cosine",
                                          temperature=16.0,
                                          top_k=None):
        """
        基于身份特征计算相似度权重
        
        Args:
            new_identity_protos: [n_new, identity_dim] 新类身份原型
            base_identity_protos: [n_base, identity_dim] 基类身份原型
            similarity_metric: 相似度度量方法
            temperature: 温度参数
            top_k: 选择top-k个最相似的基类
            
        Returns:
            similarity_weights: [n_new, n_base] 相似度权重矩阵
        """
        n_new, identity_dim = new_identity_protos.shape
        n_base = base_identity_protos.shape[0]
        
        if similarity_metric == "cosine":
            # 归一化特征
            new_norm = F.normalize(new_identity_protos, p=2, dim=-1)
            base_norm = F.normalize(base_identity_protos, p=2, dim=-1)
            
            # 计算余弦相似度
            similarity_matrix = torch.mm(new_norm, base_norm.T) * temperature
            
        elif similarity_metric == "euclidean":
            # 计算欧几里得距离
            new_expanded = new_identity_protos.unsqueeze(1)  # [n_new, 1, identity_dim]
            base_expanded = base_identity_protos.unsqueeze(0)  # [1, n_base, identity_dim]
            euclidean_dist = torch.norm(new_expanded - base_expanded, dim=2)
            
            # 转换为相似度（距离越小，相似度越高）
            similarity_matrix = -euclidean_dist * temperature
            
        elif similarity_metric == "dot_product":
            # 点积相似度
            similarity_matrix = torch.mm(new_identity_protos, base_identity_protos.T) * temperature
            
        else:
            raise ValueError(f"Unsupported similarity metric: {similarity_metric}")
        
        # 应用top-k选择
        if top_k is not None and top_k < n_base:
            # 创建稀疏权重矩阵
            sparse_similarity = torch.zeros_like(similarity_matrix)
            
            for i in range(n_new):
                topk_vals, topk_indices = torch.topk(similarity_matrix[i], top_k, dim=0)
                sparse_similarity[i].scatter_(0, topk_indices, topk_vals)
            
            similarity_matrix = sparse_similarity
        
        # 计算softmax权重
        similarity_weights = torch.softmax(similarity_matrix, dim=1)
        
        return similarity_weights
    
    def calibrate_covariance_matrices(self,
                                    new_variation_features_list,
                                    new_identity_protos,
                                    base_covariance_matrices,
                                    base_identity_protos,
                                    original_features_list=None,
                                    calibration_strength=0.5,
                                    similarity_metric="cosine",
                                    temperature=16.0,
                                    top_k=5):
        """
        校准新类的协方差矩阵
        
        Args:
            new_variation_features_list: List[Tensor] 每个新类的变化特征
            new_identity_protos: [n_new, identity_dim] 新类身份原型
            base_covariance_matrices: List[Tensor] 基类协方差矩阵
            base_identity_protos: [n_base, identity_dim] 基类身份原型
            original_features_list: List[Tensor] 每个新类的原始特征（可选）
            calibration_strength: 校准强度
            similarity_metric: 相似度度量方法
            temperature: 温度参数
            top_k: 选择top-k个最相似的基类
            
        Returns:
            calibrated_covariances: List[Tensor] 校准后的协方差矩阵
        """
        n_new = len(new_variation_features_list)
        n_base = len(base_covariance_matrices)
        
        # 计算身份相似度权重
        similarity_weights = self.compute_identity_similarity_weights(
            new_identity_protos, base_identity_protos, 
            similarity_metric, temperature, top_k
        )
        
        calibrated_covariances = []
        
        for i in range(n_new):
            # 计算当前新类的协方差矩阵
            variation_features = new_variation_features_list[i]
            original_features = original_features_list[i] if original_features_list else None
            
            current_cov = self.compute_disentangled_covariance(
                None, variation_features, original_features
            )
            
            # 获取当前新类的相似度权重
            weights = similarity_weights[i]  # [n_base]
            
            # 选择非零权重的基类协方差矩阵
            nonzero_mask = weights > 1e-8
            if nonzero_mask.sum() > 0:
                selected_weights = weights[nonzero_mask]
                selected_base_covs = [base_covariance_matrices[j] for j in range(n_base) if nonzero_mask[j]]
                
                if selected_base_covs:
                    # 加权组合基类协方差矩阵
                    weighted_base_cov = torch.zeros_like(current_cov)
                    
                    for weight, base_cov in zip(selected_weights, selected_base_covs):
                        # 确保维度匹配
                        if base_cov.shape != current_cov.shape:
                            if base_cov.shape[0] > current_cov.shape[0]:
                                # 截断基类协方差矩阵
                                base_cov_aligned = base_cov[:current_cov.shape[0], :current_cov.shape[1]]
                            else:
                                # 扩展基类协方差矩阵
                                base_cov_aligned = torch.zeros_like(current_cov)
                                base_cov_aligned[:base_cov.shape[0], :base_cov.shape[1]] = base_cov
                        else:
                            base_cov_aligned = base_cov
                        
                        weighted_base_cov += weight * base_cov_aligned
                    
                    # 校准当前协方差矩阵
                    calibrated_cov = (calibration_strength * weighted_base_cov + 
                                    (1 - calibration_strength) * current_cov)
                    
                    logging.debug(f"[Disentangled Covariance] Class {i}: used {len(selected_base_covs)} base covariances, "
                                f"weights sum: {selected_weights.sum():.3f}")
                else:
                    calibrated_cov = current_cov
                    logging.debug(f"[Disentangled Covariance] Class {i}: no valid base covariances, using original")
            else:
                calibrated_cov = current_cov
                logging.debug(f"[Disentangled Covariance] Class {i}: no similar base classes, using original")
            
            calibrated_covariances.append(calibrated_cov)
        
        return calibrated_covariances
    
    def generate_augmented_features(self, 
                                  prototypes, 
                                  covariance_matrices, 
                                  n_samples_per_class=800):
        """
        基于校准后的协方差矩阵生成增强特征
        
        Args:
            prototypes: List[Tensor] 类别原型
            covariance_matrices: List[Tensor] 协方差矩阵
            n_samples_per_class: 每个类别生成的样本数
            
        Returns:
            augmented_features: Tensor 增强特征
            augmented_labels: Tensor 对应标签
        """
        augmented_features_list = []
        augmented_labels_list = []
        
        for class_idx, (proto, cov) in enumerate(zip(prototypes, covariance_matrices)):
            try:
                # 生成多元正态分布样本
                samples = np.random.multivariate_normal(
                    proto.cpu().numpy(), 
                    cov.cpu().numpy(), 
                    n_samples_per_class
                )
                samples_tensor = torch.FloatTensor(samples)
                
                # 创建对应标签
                labels = torch.zeros(n_samples_per_class).fill_(class_idx)
                
                augmented_features_list.append(samples_tensor)
                augmented_labels_list.append(labels)
                
            except Exception as e:
                logging.warning(f"[Disentangled Covariance] Failed to generate samples for class {class_idx}: {e}")
                # 使用原型重复作为备选方案
                repeated_proto = proto.unsqueeze(0).repeat(n_samples_per_class, 1)
                labels = torch.zeros(n_samples_per_class).fill_(class_idx)
                
                augmented_features_list.append(repeated_proto)
                augmented_labels_list.append(labels)
        
        if augmented_features_list:
            augmented_features = torch.cat(augmented_features_list, dim=0)
            augmented_labels = torch.cat(augmented_labels_list, dim=0)
            return augmented_features, augmented_labels
        else:
            return None, None
