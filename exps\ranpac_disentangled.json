{"prefix": "disentangled_reproduce", "dataset": "cub", "memory_size": 0, "shuffle": true, "init_cls": 100, "increment": 10, "model_name": "ranpac", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "seed": [1993], "resume": false, "shot": 5, "use_simplecil": false, "tuned_epoch": 40, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "use_RP": true, "M": 10000, "fecam": false, "calibration": true, "knn_k": 5, "knn_distance_metric": "cosine", "knn_weight_decay": 0.1, "knn_adaptive_k": true, "knn_temperature": 16.0, "k_min": 3, "k_max": 21, "dynamic_k_method": "cosine_similarity", "cosine_temperature": 16.0, "_comment_disentangled": "=== Disentangled Adapter Configuration ===", "use_disentangled_adapter": true, "identity_bottleneck": 64, "variation_bottleneck": 64, "_comment_disentangle_loss": "=== Disentanglement Loss Configuration ===", "disentangle_loss_weight": 0.001, "use_adaptive_disentangle_loss": true, "decorrelation_weight": 0.1, "orthogonal_weight": 0.01, "mutual_info_weight": 0.05, "contrastive_weight": 0.03, "disentangle_temperature": 0.1, "disentangle_warmup_epochs": 10, "orthogonal_constraint": true, "_comment_covariance": "=== Disentangled Covariance Configuration ===", "variation_cov_weight": 0.2, "identity_similarity_weight": 1.0, "cov_regularization_strength": 1e-06, "min_samples_for_cov": 5, "identity_similarity_metric": "cosine", "identity_similarity_temperature": 16.0, "identity_similarity_top_k": 5}