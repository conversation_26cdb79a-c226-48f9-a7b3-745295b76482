import logging
import numpy as np
import torch
from torch import nn
from torch.serialization import load
from tqdm import tqdm
from torch import optim
from torch.nn import functional as F
from torch.utils.data import DataLoader
from utils.inc_net import IncrementalNet,SimpleCosineIncrementalNet,MultiBranchCosineIncrementalNet,SimpleVitNet
from models.base import BaseLearner
from utils.toolkit import target2onehot, tensor2numpy
from utils.disentanglement_losses import DisentanglementLossManager, AdaptiveDisentanglementLoss
from utils.disentangled_covariance import DisentangledCovarianceCalibrator

# Tune the model at first session with ViT adapter, and then perform RanPAC.

num_workers = 8

class Learner(BaseLearner):
    def __init__(self, args):
        super().__init__(args)
        self._network = SimpleVitNet(args, True)
        self. batch_size = args["batch_size"]
        self. init_lr = args["init_lr"]

        self.weight_decay = args["weight_decay"] if args["weight_decay"] is not None else 0.0005
        self.min_lr = args['min_lr'] if args['min_lr'] is not None else 1e-8
        self.args = args
        self.cov_mats, self.base_cov_mats = [], []
        self.proto_list = []
        self.ridge = 0
        self.knn_k = args.get("knn_k", 5)  # Number of nearest base prototypes to use during calibration
        self.knn_distance_metric = args.get("knn_distance_metric", "cosine")  # cosine, euclidean, mahalanobis
        self.knn_weight_decay = args.get("knn_weight_decay", 0.1)  # Weight decay factor based on distance
        self.knn_adaptive_k = args.get("knn_adaptive_k", False)  # Whether to use adaptive K selection
        self.knn_temperature = args.get("knn_temperature", 16.0)  # Temperature for similarity scaling

        # Dynamic K value parameters
        self.k_min = args.get("k_min", 3)  # Minimum K value for dynamic selection
        self.k_max = args.get("k_max", 21)  # Maximum K value for dynamic selection
        self.dynamic_k_method = args.get("dynamic_k_method", "cosine_similarity")  # Method for dynamic K selection
        self.cosine_temperature = args.get("cosine_temperature", 16.0)  # Temperature for scaled cosine similarity

        # Storage for base class similarity statistics
        self.base_similarity_stats = {
            'sim_min': None,
            'sim_max': None,
            'computed': False
        }

        # 解耦适配器相关参数
        self.use_disentangled_adapter = args.get("use_disentangled_adapter", False)
        self.disentangle_loss_weight = args.get("disentangle_loss_weight", 0.1)
        self.use_adaptive_disentangle_loss = args.get("use_adaptive_disentangle_loss", True)

        # 向后兼容性检查
        if self.use_disentangled_adapter:
            logging.info(f"[Disentangled Adapter] Enabled with loss_weight={self.disentangle_loss_weight}")
        else:
            logging.info(f"[Disentangled Adapter] Disabled - using original RANPAC implementation")

        # 解耦损失管理器
        if self.use_disentangled_adapter:
            if self.use_adaptive_disentangle_loss:
                self.disentangle_loss_manager = AdaptiveDisentanglementLoss(
                    decorrelation_weight=args.get("decorrelation_weight", 1.0),
                    orthogonal_weight=args.get("orthogonal_weight", 0.1),
                    mutual_info_weight=args.get("mutual_info_weight", 0.5),
                    contrastive_weight=args.get("contrastive_weight", 0.3),
                    temperature=args.get("disentangle_temperature", 0.1),
                    warmup_epochs=args.get("disentangle_warmup_epochs", 10),
                    total_epochs=args.get("tuned_epoch", 100)
                )
            else:
                self.disentangle_loss_manager = DisentanglementLossManager(
                    decorrelation_weight=args.get("decorrelation_weight", 1.0),
                    orthogonal_weight=args.get("orthogonal_weight", 0.1),
                    mutual_info_weight=args.get("mutual_info_weight", 0.5),
                    contrastive_weight=args.get("contrastive_weight", 0.3),
                    temperature=args.get("disentangle_temperature", 0.1)
                )
            self.disentangle_loss_manager.to(self._device)
            logging.info(f"[Disentangled Adapter] Initialized with adaptive={self.use_adaptive_disentangle_loss}")

            # 初始化解耦协方差校准器
            self.covariance_calibrator = DisentangledCovarianceCalibrator(
                variation_cov_weight=args.get("variation_cov_weight", 0.8),
                identity_similarity_weight=args.get("identity_similarity_weight", 1.0),
                regularization_strength=args.get("cov_regularization_strength", 1e-6),
                min_samples_for_cov=args.get("min_samples_for_cov", 5)
            )
            logging.info(f"[Disentangled Adapter] Covariance calibrator initialized")
        else:
            self.disentangle_loss_manager = None
            self.covariance_calibrator = None

        # 存储解耦特征用于协方差计算
        self.identity_features_list = []
        self.variation_features_list = []

        # 验证配置
        self._validate_disentangled_config()

    def _validate_disentangled_config(self):
        """验证解耦适配器配置的有效性"""
        if not self.use_disentangled_adapter:
            return

        try:
            # 检查必要的参数
            required_params = [
                "identity_bottleneck", "variation_bottleneck",
                "disentangle_loss_weight", "variation_cov_weight"
            ]

            missing_params = []
            for param in required_params:
                if param not in self.args:
                    missing_params.append(param)

            if missing_params:
                logging.warning(f"[Disentangled Adapter] Missing parameters: {missing_params}, using defaults")

            # 检查参数范围
            if self.disentangle_loss_weight < 0 or self.disentangle_loss_weight > 1:
                logging.warning(f"[Disentangled Adapter] disentangle_loss_weight={self.disentangle_loss_weight} "
                              f"is outside recommended range [0, 1]")

            # 检查网络是否支持解耦适配器
            if hasattr(self._network, 'backbone'):
                if not hasattr(self._network.backbone, 'extract_disentangled_features'):
                    logging.warning(f"[Disentangled Adapter] Network backbone does not support disentangled features, "
                                  f"some features may not work properly")

            logging.info(f"[Disentangled Adapter] Configuration validation completed")

        except Exception as e:
            logging.error(f"[Disentangled Adapter] Configuration validation failed: {e}")
            logging.warning(f"[Disentangled Adapter] Disabling disentangled adapter due to configuration errors")
            self.use_disentangled_adapter = False

    def after_task(self):
        self._known_classes = self._total_classes

        # 如果是base task完成，预计算基类相似度统计
        if (self._cur_task == 0 and hasattr(self, 'proto_list') and
            len(self.proto_list) >= self.args['init_cls'] and
            self.dynamic_k_method == "cosine_similarity"):
            try:
                base_protos = torch.stack(self.proto_list[:self.args['init_cls']])
                self._compute_base_similarity_stats(base_protos)
                logging.info(f'[Dynamic-K] Base similarity stats computed after base task')
            except Exception as e:
                logging.warning(f'[Dynamic-K] Failed to compute base similarity stats: {e}')
        
    def replace_fc(self, trainloader, model, args):
        model = model.eval()
        embedding_list = []
        label_list = []
        cur_proto_list = []

        # 解耦特征存储
        identity_features_list = []
        variation_features_list = []

        with torch.no_grad():
            for i, batch in enumerate(trainloader):
                (_,data, label) = batch
                data = data.to(self._device)
                label = label.to(self._device)

                # 紧急修复：暂时禁用解耦特征提取，确保基本功能正常
                if self.use_disentangled_adapter and True:  # 临时禁用
                    # 提取解耦特征
                    try:
                        if hasattr(model.backbone, 'extract_disentangled_features'):
                            combined_features, identity_features, variation_features, _ = model.backbone.extract_disentangled_features(data)
                            embedding = combined_features
                            if identity_features is not None and variation_features is not None:
                                identity_features_list.append(identity_features.cpu())
                                variation_features_list.append(variation_features.cpu())
                        else:
                            # 回退到普通特征提取
                            embedding = model.extract_vector(data)
                    except Exception as e:
                        logging.warning(f"[Disentangled Adapter] Failed to extract disentangled features: {e}, falling back to normal extraction")
                        embedding = model.extract_vector(data)
                else:
                    # 使用原始特征提取方法
                    embedding = model.extract_vector(data)
                    logging.debug(f"[Emergency Fix] Using original feature extraction, embedding shape: {embedding.shape}")

                embedding_list.append(embedding.cpu())
                label_list.append(label.cpu())

        embedding_list = torch.cat(embedding_list, dim=0)
        label_list = torch.cat(label_list, dim=0)

        # 处理解耦特征
        if self.use_disentangled_adapter and identity_features_list and variation_features_list:
            identity_features_all = torch.cat(identity_features_list, dim=0)
            variation_features_all = torch.cat(variation_features_list, dim=0)
            logging.info(f"[Disentangled Adapter] Extracted identity features: {identity_features_all.shape}, variation features: {variation_features_all.shape}")
        else:
            identity_features_all = None
            variation_features_all = None
        
        if self.args['calibration']:
            class_list=np.unique(self.train_dataset.labels)
            emb_list, cls_list = [], []

            # 存储解耦特征的原型和协方差
            identity_proto_list = []
            variation_proto_list = []
            variation_features_by_class = []
            original_features_by_class = []

            for class_index in class_list:
                data_index=(label_list==class_index).nonzero().squeeze(-1)
                embedding=embedding_list[data_index]
                proto = embedding.mean(0)
                self.proto_list.append(proto)
                cur_proto_list.append(proto)

                # 存储每个类别的特征用于后续校准
                original_features_by_class.append(embedding)

                # 计算协方差矩阵
                if self.use_disentangled_adapter and identity_features_all is not None and variation_features_all is not None:
                    # 使用解耦特征计算协方差
                    class_identity_features = identity_features_all[data_index]
                    class_variation_features = variation_features_all[data_index]

                    # 身份特征原型（用于相似度计算）
                    identity_proto = class_identity_features.mean(0)
                    identity_proto_list.append(identity_proto)

                    # 变化特征原型和协方差（用于协方差建模）
                    variation_proto = class_variation_features.mean(0)
                    variation_proto_list.append(variation_proto)
                    variation_features_by_class.append(class_variation_features)

                    # 使用协方差校准器计算协方差矩阵
                    if self.covariance_calibrator is not None:
                        cov = self.covariance_calibrator.compute_disentangled_covariance(
                            class_identity_features, class_variation_features, embedding
                        )
                    else:
                        # 回退到简单的加权组合
                        variation_cov = torch.cov(class_variation_features.T)
                        original_cov = torch.cov(embedding.T)
                        variation_weight = args.get("variation_cov_weight", 0.7)
                        cov = variation_weight * variation_cov + (1 - variation_weight) * original_cov

                    logging.debug(f'[Disentangled Adapter] Class {class_index}: identity_proto shape {identity_proto.shape}, '
                                f'variation_proto shape {variation_proto.shape}, cov shape {cov.shape}')
                else:
                    # 使用原始方法计算协方差
                    cov = torch.cov(embedding.T)

                if self._cur_task == 0:
                    self.base_cov_mats.append(cov)
                self.cov_mats.append(cov)

            # 存储解耦特征原型用于后续任务
            if self.use_disentangled_adapter and identity_proto_list:
                if not hasattr(self, 'identity_proto_list'):
                    self.identity_proto_list = []
                    self.variation_proto_list = []

                self.identity_proto_list.extend(identity_proto_list)
                self.variation_proto_list.extend(variation_proto_list)
                    
            if self._cur_task > 0:
                base_protos = torch.stack(self.proto_list[:args['init_cls']])
                softmax_t = 16
                alpha = 0.9  # 0.9 for CUB200, Aircrafts, Cars and 0.75 for CIFAR100

                cur_proto_list = torch.stack(cur_proto_list).detach().cpu()  # [n_new, feat_dim]

                # ----- Enhanced KNN-based sparse weighting (紧急修复：禁用解耦特征) -----
                if self.use_disentangled_adapter and True and hasattr(self, 'identity_proto_list') and len(self.identity_proto_list) >= args['init_cls']:
                    # 使用身份特征计算相似度权重
                    base_identity_protos = torch.stack(self.identity_proto_list[:args['init_cls']])
                    cur_identity_protos = torch.stack(identity_proto_list).detach().cpu()

                    # 基于身份特征计算KNN权重
                    norm_weights = self._compute_knn_weights_disentangled(
                        cur_identity_protos, base_identity_protos, args['init_cls']
                    )

                    logging.info(f'[Disentangled Adapter] Using identity features for KNN weight computation: '
                               f'cur_identity shape {cur_identity_protos.shape}, base_identity shape {base_identity_protos.shape}')
                else:
                    # 使用原始方法
                    norm_weights = self._compute_knn_weights(cur_proto_list, base_protos, args['init_cls'])
                    logging.info(f'[Emergency Fix] Using original KNN weight computation')
                
                delta_protos = torch.matmul(norm_weights, base_protos)
                
                updated_protos = alpha * cur_proto_list + (1-alpha) * delta_protos
                
                beta = 0.5

                # 使用解耦协方差校准器进行批量校准 (紧急修复：暂时禁用)
                if (self.use_disentangled_adapter and True and self.covariance_calibrator is not None and
                    variation_features_by_class and hasattr(self, 'identity_proto_list') and
                    len(self.identity_proto_list) >= args['init_cls']):

                    # 准备数据进行批量校准
                    new_identity_protos = torch.stack(identity_proto_list)
                    base_identity_protos = torch.stack(self.identity_proto_list[:args['init_cls']])

                    # 使用解耦协方差校准器
                    calibrated_covariances = self.covariance_calibrator.calibrate_covariance_matrices(
                        variation_features_by_class,
                        new_identity_protos,
                        self.base_cov_mats[:args['init_cls']],
                        base_identity_protos,
                        original_features_by_class,
                        calibration_strength=beta,
                        similarity_metric=args.get("identity_similarity_metric", "cosine"),
                        temperature=args.get("identity_similarity_temperature", 16.0),
                        top_k=args.get("identity_similarity_top_k", 5)
                    )

                    # 更新协方差矩阵
                    for idd, class_index in enumerate(class_list):
                        self.proto_list[class_index] = updated_protos[idd]
                        self.cov_mats[class_index] = calibrated_covariances[idd]

                        logging.debug(f'[Disentangled Covariance] class {class_index}: calibrated covariance shape {calibrated_covariances[idd].shape}')

                    logging.info(f'[Disentangled Covariance] Calibrated {len(class_list)} covariance matrices using disentangled features')
                else:
                    # 回退到原始方法
                    for idd, class_index in enumerate(class_list):
                        self.proto_list[class_index] = updated_protos[idd]

                        # Enhanced KNN-based covariance calibration
                        knn_weights = norm_weights[idd]  # [init_cls]

                        # Only use non-zero weights (selected neighbors)
                        nonzero_mask = knn_weights > 0
                        selected_weights = knn_weights[nonzero_mask]
                        selected_base_covs = torch.stack([self.base_cov_mats[i] for i in range(len(self.base_cov_mats)) if nonzero_mask[i]], 0)

                        if len(selected_base_covs) > 0:
                            # Weighted combination of selected base covariance matrices
                            delta_covs = selected_weights.view(-1, 1, 1) * selected_base_covs
                            weighted_base_cov = torch.sum(delta_covs, 0)

                            # Combine with current covariance matrix
                            self.cov_mats[class_index] = beta * weighted_base_cov + self.cov_mats[class_index] * beta

                            logging.debug(f'[KNN-FeCAM] class {class_index}: used {len(selected_base_covs)} base covariances, '
                                        f'weights sum: {selected_weights.sum():.3f}')
                        else:
                            # Fallback to original method if no neighbors selected
                            delta_covs = norm_weights[idd].view(args['init_cls'], 1, 1)*torch.stack(self.base_cov_mats[:args['init_cls']],0)
                            self.cov_mats[class_index] = beta*torch.sum(delta_covs,0) + self.cov_mats[class_index]*beta

                # 生成增强特征
                if self.use_disentangled_adapter and self.covariance_calibrator is not None:
                    # 使用协方差校准器生成增强特征
                    current_protos = [self.proto_list[class_index] for class_index in class_list]
                    current_covs = [self.cov_mats[class_index] for class_index in class_list]

                    augmented_features, augmented_labels = self.covariance_calibrator.generate_augmented_features(
                        current_protos, current_covs, n_samples_per_class=800
                    )

                    if augmented_features is not None and augmented_labels is not None:
                        # 调整标签以匹配实际类别索引
                        for i, class_index in enumerate(class_list):
                            mask = augmented_labels == i
                            augmented_labels[mask] = class_index

                        emb_list.append(augmented_features)
                        cls_list.append(augmented_labels)

                        logging.info(f'[Disentangled Covariance] Generated {augmented_features.shape[0]} augmented samples')
                    else:
                        # 回退到原始方法
                        for idd, class_index in enumerate(class_list):
                            xx = np.random.multivariate_normal(self.proto_list[class_index], self.cov_mats[class_index], 800)
                            xx = torch.FloatTensor(xx)
                            emb_list.append(xx)
                            cls_list.append(torch.zeros(xx.shape[0]).fill_(class_index))
                else:
                    # 原始增强方法
                    for idd, class_index in enumerate(class_list):
                        xx = np.random.multivariate_normal(self.proto_list[class_index], self.cov_mats[class_index], 800)
                        xx = torch.FloatTensor(xx)
                        emb_list.append(xx)
                        cls_list.append(torch.zeros(xx.shape[0]).fill_(class_index))
            
                embedding_list = torch.cat(emb_list,0)
                label_list = torch.cat(cls_list,0)
        
        Y = target2onehot(label_list, self.args["nb_classes"])
        Features_h = F.relu(embedding_list @ self.W_rand.cpu())
        self.Q = self.Q + Features_h.T @ Y
        self.G = self.G + Features_h.T @ Features_h
        if self._cur_task == 0:
            self.ridge = self.optimise_ridge_parameter(Features_h, Y)
        Wo = torch.linalg.solve(self.G + self.ridge*torch.eye(self.G.size(dim=0)), self.Q).T  # better numerical stability than .invv
        self._network.fc.weight.data = Wo[0:self._network.fc.weight.shape[0],:].to(self._device)
        
        return model

    def setup_RP(self):
        M = self.args['M']
        self._network.fc.weight = nn.Parameter(torch.Tensor(self._network.fc.out_features, M).to(self._device)).requires_grad_(False) # num classes in task x M
        self._network.RP_dim = M
        self.W_rand = torch.randn(self._network.fc.in_features, M).to(self._device)
        self._network.W_rand = self.W_rand

        self.Q = torch.zeros(M, self.args["nb_classes"])
        self.G = torch.zeros(M, M)

    def optimise_ridge_parameter(self, Features, Y):
        ridges = 10.0 ** np.arange(1, 9)
        num_val_samples = int(Features.shape[0] * 0.8)
        losses = []
        Q_val = Features[0:num_val_samples, :].T @ Y[0:num_val_samples, :]
        G_val = Features[0:num_val_samples, :].T @ Features[0:num_val_samples, :]
        for ridge in ridges:
            Wo = torch.linalg.solve(G_val + ridge*torch.eye(G_val.size(dim=0)), Q_val).T #better nmerical stability than .inv
            Y_train_pred = Features[num_val_samples::,:] @ Wo.T
            losses.append(F.mse_loss(Y_train_pred, Y[num_val_samples::, :]))
        ridge = ridges[np.argmin(np.array(losses))]
        print('selected lambda = ',ridge)
        return ridge

    def _compute_similarity_matrix(self, query_protos, base_protos):
        """
        Compute similarity matrix between query prototypes and base prototypes
        Args:
            query_protos: [n_new, feat_dim] - new class prototypes
            base_protos: [init_cls, feat_dim] - base class prototypes
        Returns:
            similarity_matrix: [n_new, init_cls] - similarity scores
        """
        if self.knn_distance_metric == "cosine":
            # Normalize prototypes for cosine similarity
            query_norm = F.normalize(query_protos, p=2, dim=-1)
            base_norm = F.normalize(base_protos, p=2, dim=-1)
            sim_matrix = torch.mm(query_norm, base_norm.T) * self.knn_temperature
        elif self.knn_distance_metric == "euclidean":
            # Compute negative euclidean distance (higher is more similar)
            query_expanded = query_protos.unsqueeze(1)  # [n_new, 1, feat_dim]
            base_expanded = base_protos.unsqueeze(0)    # [1, init_cls, feat_dim]
            euclidean_dist = torch.norm(query_expanded - base_expanded, dim=2)  # [n_new, init_cls]
            sim_matrix = -euclidean_dist * self.knn_temperature
        elif self.knn_distance_metric == "mahalanobis":
            # Use base covariance matrices for Mahalanobis distance
            sim_matrix = self._compute_mahalanobis_similarity(query_protos, base_protos)
        else:
            raise ValueError(f"Unsupported distance metric: {self.knn_distance_metric}")

        return sim_matrix

    def _compute_mahalanobis_similarity(self, query_protos, base_protos):
        """Compute Mahalanobis-based similarity using base class covariance matrices"""
        n_new, feat_dim = query_protos.shape
        init_cls = base_protos.shape[0]
        sim_matrix = torch.zeros(n_new, init_cls)

        for i, query_proto in enumerate(query_protos):
            for j, base_proto in enumerate(base_protos):
                if j < len(self.base_cov_mats):
                    # Use base class covariance matrix
                    cov_inv = torch.linalg.pinv(self.base_cov_mats[j]).float()
                    diff = query_proto - base_proto
                    mahal_dist = torch.sqrt(torch.matmul(torch.matmul(diff, cov_inv), diff))
                    sim_matrix[i, j] = -mahal_dist * self.knn_temperature
                else:
                    # Fallback to euclidean distance
                    euclidean_dist = torch.norm(query_proto - base_proto)
                    sim_matrix[i, j] = -euclidean_dist * self.knn_temperature

        return sim_matrix

    def _adaptive_k_selection(self, similarity_matrix, base_k):
        """
        Adaptively select K based on similarity distribution
        Args:
            similarity_matrix: [n_new, init_cls] - similarity scores
            base_k: base K value from configuration
        Returns:
            k_values: [n_new] - adaptive K for each new class
        """
        n_new, init_cls = similarity_matrix.shape
        k_values = []

        for i in range(n_new):
            sim_scores = similarity_matrix[i]

            # Method 1: Use similarity threshold
            sorted_sims, _ = torch.sort(sim_scores, descending=True)
            if len(sorted_sims) > 1:
                # Find elbow point in similarity scores
                diffs = sorted_sims[:-1] - sorted_sims[1:]
                if len(diffs) > 0:
                    elbow_idx = torch.argmax(diffs).item() + 1
                    adaptive_k = min(max(elbow_idx, 1), base_k, init_cls)
                else:
                    adaptive_k = min(base_k, init_cls)
            else:
                adaptive_k = min(base_k, init_cls)

            k_values.append(adaptive_k)

        return k_values

    def _compute_scaled_cosine_similarity(self, query_protos, base_protos):
        """
        计算缩放余弦相似度: S = (μ_b · μ_n) / (||μ_b|| · ||μ_n||) · τ
        Args:
            query_protos: [n_query, feat_dim] - 查询原型
            base_protos: [n_base, feat_dim] - 基类原型
        Returns:
            scaled_sim: [n_query, n_base] - 缩放余弦相似度矩阵
        """
        # 归一化向量
        query_norm = F.normalize(query_protos, p=2, dim=-1)
        base_norm = F.normalize(base_protos, p=2, dim=-1)

        # 计算余弦相似度
        cosine_sim = torch.mm(query_norm, base_norm.T)

        # 应用温度缩放
        scaled_sim = cosine_sim * self.cosine_temperature

        return scaled_sim

    def _compute_base_similarity_stats(self, base_protos):
        """
        预计算基类间相似度统计信息
        Args:
            base_protos: [init_cls, feat_dim] - 基类原型
        """
        init_cls = base_protos.shape[0]
        avg_similarities = []

        logging.info(f'[Dynamic-K] Computing base similarity stats for {init_cls} base classes')

        # 计算每个基类与其他基类的平均相似度
        for i in range(init_cls):
            similarities = []
            for j in range(init_cls):
                if i != j:
                    # 使用缩放余弦相似度
                    sim = self._compute_scaled_cosine_similarity(
                        base_protos[i:i+1], base_protos[j:j+1]
                    )[0, 0]
                    similarities.append(sim.item())

            if len(similarities) > 0:
                avg_sim = sum(similarities) / len(similarities)
                avg_similarities.append(avg_sim)

        if len(avg_similarities) > 0:
            # 确定范围
            self.base_similarity_stats['sim_min'] = min(avg_similarities)
            self.base_similarity_stats['sim_max'] = max(avg_similarities)
            self.base_similarity_stats['computed'] = True

            logging.info(f'[Dynamic-K] Base similarity range: '
                        f'[{self.base_similarity_stats["sim_min"]:.4f}, '
                        f'{self.base_similarity_stats["sim_max"]:.4f}]')
        else:
            logging.warning('[Dynamic-K] No base similarities computed, using default range')
            self.base_similarity_stats['sim_min'] = 0.0
            self.base_similarity_stats['sim_max'] = self.cosine_temperature
            self.base_similarity_stats['computed'] = True

    def _compute_dynamic_k_values(self, cur_proto_list, base_protos):
        """
        基于余弦相似度计算动态K值
        Args:
            cur_proto_list: [n_new, feat_dim] - 新类原型
            base_protos: [init_cls, feat_dim] - 基类原型
        Returns:
            k_values: [n_new] - 每个新类的动态K值
        """
        if not self.base_similarity_stats['computed']:
            # 如果还没有预计算，先计算基类相似度统计
            self._compute_base_similarity_stats(base_protos)

        n_new = cur_proto_list.shape[0]
        init_cls = base_protos.shape[0]
        k_values = []

        sim_min = self.base_similarity_stats['sim_min']
        sim_max = self.base_similarity_stats['sim_max']

        logging.info(f'[Dynamic-K] Computing dynamic K for {n_new} new classes')

        for i in range(n_new):
            # 计算新类与所有基类的缩放余弦相似度
            similarities = self._compute_scaled_cosine_similarity(
                cur_proto_list[i:i+1], base_protos
            )[0]  # [init_cls]

            # 计算平均相似度
            avg_similarity = similarities.mean().item()

            # 归一化到[0,1]
            if sim_max > sim_min:
                normalized_similarity = (avg_similarity - sim_min) / (sim_max - sim_min)
                # 确保在[0,1]范围内
                normalized_similarity = max(0.0, min(1.0, normalized_similarity))
            else:
                # 如果范围为0，使用中间值
                normalized_similarity = 0.5
                logging.warning(f'[Dynamic-K] sim_max == sim_min, using normalized_similarity=0.5')

            # 反向线性映射到K值: K = K_max - (K_max - K_min) * normalized_similarity
            k_float = self.k_max - (self.k_max - self.k_min) * normalized_similarity
            k_final = round(k_float)

            # 确保K值在合理范围内
            k_final = max(self.k_min, min(self.k_max, min(k_final, init_cls)))
            k_values.append(k_final)

            logging.debug(f'[Dynamic-K] Class {i}: avg_sim={avg_similarity:.4f}, '
                         f'normalized={normalized_similarity:.4f}, '
                         f'k_float={k_float:.2f}, K_final={k_final}')

        logging.info(f'[Dynamic-K] Computed K values: {k_values}')
        return k_values

    def _compute_knn_weights(self, cur_proto_list, base_protos, init_cls):
        """
        Enhanced KNN-based weight computation with dynamic K selection
        Args:
            cur_proto_list: [n_new, feat_dim] - current task prototypes
            base_protos: [init_cls, feat_dim] - base class prototypes
            init_cls: number of base classes
        Returns:
            norm_weights: [n_new, init_cls] - normalized sparse weights
        """
        n_new = cur_proto_list.shape[0]

        # Compute similarity matrix using selected distance metric
        if self.knn_distance_metric == "cosine" and self.dynamic_k_method == "cosine_similarity":
            # Use scaled cosine similarity for dynamic K method
            sim_matrix = self._compute_scaled_cosine_similarity(cur_proto_list, base_protos)
        else:
            # Use original similarity computation
            sim_matrix = self._compute_similarity_matrix(cur_proto_list, base_protos)

        # Dynamic/Adaptive K selection
        if self.knn_adaptive_k and self.dynamic_k_method == "cosine_similarity":
            # Use dynamic K based on cosine similarity
            k_values = self._compute_dynamic_k_values(cur_proto_list, base_protos)
            logging.info(f'[KNN] task {self._cur_task}, dynamic K values: {k_values}')
        elif self.knn_adaptive_k:
            # Use original adaptive K selection
            k_values = self._adaptive_k_selection(sim_matrix, self.knn_k)
            logging.info(f'[KNN] task {self._cur_task}, adaptive K values: {k_values}')
        else:
            # Use fixed K value
            k_values = [min(self.knn_k, init_cls)] * n_new
            logging.info(f'[KNN] task {self._cur_task}, fixed K={k_values[0]}')

        # Initialize sparse weight matrix
        norm_weights = torch.zeros(n_new, init_cls)

        for i in range(n_new):
            K = k_values[i]
            sim_scores = sim_matrix[i]

            if K < init_cls:
                # Select top-K most similar base prototypes
                topk_val, topk_idx = torch.topk(sim_scores, K, dim=0)

                # Apply distance-based weight decay
                if self.knn_weight_decay > 0:
                    # Convert similarities to distances (assuming higher similarity = lower distance)
                    max_sim = topk_val.max()
                    distances = max_sim - topk_val
                    decay_weights = torch.exp(-self.knn_weight_decay * distances)
                    topk_val = topk_val * decay_weights

                # Compute softmax weights over top-K neighbors
                weights_k = torch.softmax(topk_val, dim=0)

                # Assign weights to corresponding positions
                norm_weights[i].scatter_(0, topk_idx, weights_k)

                logging.debug(f'[KNN] class {i}: selected neighbors {topk_idx.tolist()}, '
                            f'weights {weights_k.tolist()[:3]}...')
            else:
                # Fallback to dense softmax when K >= #base classes
                norm_weights[i] = torch.softmax(sim_scores, dim=0)
                logging.debug(f'[KNN] class {i}: using dense weights (K={K} >= init_cls={init_cls})')

        # Log sparsity statistics
        sparsity = (norm_weights == 0).float().mean().item()
        logging.info(f'[KNN] task {self._cur_task}, weight sparsity: {sparsity:.3f}, '
                    f'distance_metric: {self.knn_distance_metric}')

        return norm_weights

    def _compute_knn_weights_disentangled(self, cur_identity_protos, base_identity_protos, init_cls):
        """
        基于解耦身份特征计算KNN权重

        Args:
            cur_identity_protos: [n_new, identity_dim] - 当前任务的身份原型
            base_identity_protos: [init_cls, identity_dim] - 基类身份原型
            init_cls: 基类数量

        Returns:
            norm_weights: [n_new, init_cls] - 归一化稀疏权重
        """
        n_new = cur_identity_protos.shape[0]

        # 使用身份特征计算相似度矩阵
        if self.knn_distance_metric == "cosine" and self.dynamic_k_method == "cosine_similarity":
            sim_matrix = self._compute_scaled_cosine_similarity(cur_identity_protos, base_identity_protos)
        else:
            sim_matrix = self._compute_similarity_matrix(cur_identity_protos, base_identity_protos)

        # 动态K选择（基于身份特征）
        if self.knn_adaptive_k and self.dynamic_k_method == "cosine_similarity":
            k_values = self._compute_dynamic_k_values(cur_identity_protos, base_identity_protos)
            logging.info(f'[Disentangled KNN] task {self._cur_task}, dynamic K values: {k_values}')
        elif self.knn_adaptive_k:
            k_values = self._adaptive_k_selection(sim_matrix, self.knn_k)
            logging.info(f'[Disentangled KNN] task {self._cur_task}, adaptive K values: {k_values}')
        else:
            k_values = [min(self.knn_k, init_cls)] * n_new
            logging.info(f'[Disentangled KNN] task {self._cur_task}, fixed K={k_values[0]}')

        # 初始化稀疏权重矩阵
        norm_weights = torch.zeros(n_new, init_cls)

        for i in range(n_new):
            K = k_values[i]
            sim_scores = sim_matrix[i]

            if K < init_cls:
                # 选择top-K最相似的基类原型
                topk_val, topk_idx = torch.topk(sim_scores, K, dim=0)

                # 应用基于距离的权重衰减
                if self.knn_weight_decay > 0:
                    max_sim = topk_val.max()
                    distances = max_sim - topk_val
                    decay_weights = torch.exp(-self.knn_weight_decay * distances)
                    topk_val = topk_val * decay_weights

                # 计算softmax权重
                weights_k = torch.softmax(topk_val, dim=0)

                # 分配权重到对应位置
                norm_weights[i].scatter_(0, topk_idx, weights_k)

                logging.debug(f'[Disentangled KNN] class {i}: selected neighbors {topk_idx.tolist()}, '
                            f'weights {weights_k.tolist()[:3]}...')
            else:
                # 当K >= 基类数量时回退到密集softmax
                norm_weights[i] = torch.softmax(sim_scores, dim=0)
                logging.debug(f'[Disentangled KNN] class {i}: using dense weights (K={K} >= init_cls={init_cls})')

        # 记录稀疏性统计
        sparsity = (norm_weights == 0).float().mean().item()
        logging.info(f'[Disentangled KNN] task {self._cur_task}, weight sparsity: {sparsity:.3f}, '
                    f'distance_metric: {self.knn_distance_metric}')

        return norm_weights

    def incremental_train(self, data_manager):
        self._cur_task += 1
        self._total_classes = self._known_classes + data_manager.get_task_size(self._cur_task)
        self._network.to(self._device)
        self._network.update_fc(self._total_classes)
        logging.info("Learning on {}-{}".format(self._known_classes, self._total_classes))

        if self._cur_task > 0:
            self.shot = self.args["shot"]
        else:
            self.shot = None

        train_dataset = data_manager.get_dataset(np.arange(self._known_classes, self._total_classes),source="train", mode="train", shot=self.shot,)
        self.train_dataset=train_dataset
        self.data_manager=data_manager
        self.train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True, num_workers=num_workers)

        test_dataset = data_manager.get_dataset(np.arange(0, self._total_classes), source="test", mode="test" )
        self.test_loader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False, num_workers=num_workers)

        train_dataset_for_protonet = data_manager.get_dataset(np.arange(self._known_classes, self._total_classes), source="train", mode="test", shot=self.shot,)
        self.train_loader_for_protonet = DataLoader(train_dataset_for_protonet, batch_size=self.batch_size, shuffle=True, num_workers=num_workers)

        if len(self._multiple_gpus) > 1:
            print('Multiple GPUs')
            self._network = nn.DataParallel(self._network, self._multiple_gpus)
        self._train(self.train_loader, self.test_loader, self.train_loader_for_protonet)

        if len(self._multiple_gpus) > 1:
            self._network = self._network.module

    def _train(self, train_loader, test_loader, train_loader_for_protonet):
        self._network.to(self._device)
        
        if self._cur_task == 0:
            # show total parameters and trainable parameters
            total_params = sum(p.numel() for p in self._network.parameters())
            print(f'{total_params:,} total parameters.')
            total_trainable_params = sum(
                p.numel() for p in self._network.parameters() if p.requires_grad)
            print(f'{total_trainable_params:,} training parameters.')
            if total_params != total_trainable_params:
                for name, param in self._network.named_parameters():
                    if param.requires_grad:
                        print(name, param.numel())
            if self.args['optimizer'] == 'sgd':
                optimizer = optim.SGD(self._network.parameters(), momentum=0.9, lr=self.init_lr,weight_decay=self.weight_decay)
            elif self.args['optimizer'] == 'adam':
                optimizer = optim.AdamW(self._network.parameters(), lr=self.init_lr, weight_decay=self.weight_decay)
            scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=self.args['tuned_epoch'], eta_min=self.min_lr)
            if not self.args['resume']:
                self._init_train(train_loader, test_loader, optimizer, scheduler)
                self.save_checkpoint("weights/{}_{}_{}_{}".format(self.args["dataset"],self.args["model_name"],self.args["init_cls"],self.args["increment"]))
                self._network.to(self._device)
            else:
                self._network.load_state_dict(torch.load("weights/{}_{}_{}_{}_{}.pkl".format(self.args["dataset"],self.args["model_name"],self.args["init_cls"],self.args["increment"],self._cur_task))["model_state_dict"])
                self._network.to(self._device)
        else:
            pass
        if self._cur_task == 0 and self.args["use_RP"]:
            self.setup_RP()
        self.replace_fc(train_loader_for_protonet, self._network, self.args)

    def _init_train(self, train_loader, test_loader, optimizer, scheduler):
        prog_bar = tqdm(range(self.args['tuned_epoch']))

        # 更新解耦损失管理器的epoch信息
        if self.use_disentangled_adapter and self.disentangle_loss_manager is not None:
            if hasattr(self.disentangle_loss_manager, 'update_epoch'):
                self.disentangle_loss_manager.update_epoch(0)

        for _, epoch in enumerate(prog_bar):
            self._network.train()
            losses = 0.0
            disentangle_losses = 0.0
            correct, total = 0, 0

            # 更新解耦损失权重
            if self.use_disentangled_adapter and self.disentangle_loss_manager is not None:
                if hasattr(self.disentangle_loss_manager, 'update_epoch'):
                    self.disentangle_loss_manager.update_epoch(epoch)

            for i, (_, inputs, targets) in enumerate(train_loader):
                inputs, targets = inputs.to(self._device), targets.to(self._device)

                # 紧急修复：暂时禁用解耦损失计算
                if self.use_disentangled_adapter and True:
                    # 获取解耦特征和损失
                    try:
                        if hasattr(self._network.backbone, 'forward') and hasattr(self._network.backbone, 'extract_disentangled_features'):
                            # 前向传播获取logits
                            network_output = self._network(inputs)
                            logits = network_output["logits"]

                            # 单独提取解耦特征用于计算解耦损失
                            _, identity_features, variation_features, backbone_disentangle_loss = self._network.backbone.extract_disentangled_features(inputs)

                            # 计算分类损失
                            classification_loss = F.cross_entropy(logits, targets)

                            # 计算解耦损失
                            total_disentangle_loss = 0.0
                            if identity_features is not None and variation_features is not None:
                                # 使用解耦损失管理器计算损失
                                disentangle_loss, loss_dict = self.disentangle_loss_manager(
                                    identity_features, variation_features, targets
                                )
                                total_disentangle_loss += disentangle_loss

                                # 添加backbone的解耦损失
                                if backbone_disentangle_loss is not None:
                                    total_disentangle_loss += backbone_disentangle_loss

                                # 记录详细损失信息
                                if i % 50 == 0:  # 每50个batch记录一次
                                    logging.debug(f'[Disentangled Training] Epoch {epoch}, Batch {i}: '
                                                f'classification_loss={classification_loss.item():.4f}, '
                                                f'disentangle_loss={disentangle_loss.item():.4f}, '
                                                f'loss_dict={loss_dict}')

                            # 总损失
                            loss = classification_loss + self.disentangle_loss_weight * total_disentangle_loss
                            disentangle_losses += total_disentangle_loss.item() if total_disentangle_loss != 0.0 else 0.0
                        else:
                            # 回退到普通训练
                            logits = self._network(inputs)["logits"]
                            loss = F.cross_entropy(logits, targets)
                    except Exception as e:
                        logging.warning(f'[Disentangled Training] Error in disentangled forward pass: {e}, falling back to normal training')
                        logits = self._network(inputs)["logits"]
                        loss = F.cross_entropy(logits, targets)
                else:
                    # 普通训练
                    logits = self._network(inputs)["logits"]
                    loss = F.cross_entropy(logits, targets)

                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                losses += loss.item()

                _, preds = torch.max(logits, dim=1)
                correct += preds.eq(targets.expand_as(preds)).cpu().sum()
                total += len(targets)

            scheduler.step()
            train_acc = np.around(tensor2numpy(correct) * 100 / total, decimals=2)

            test_acc = self._compute_accuracy(self._network, test_loader)

            if self.use_disentangled_adapter and disentangle_losses > 0:
                info = "Task {}, Epoch {}/{} => Loss {:.3f}, Disentangle_Loss {:.3f}, Train_accy {:.2f}, Test_accy {:.2f}".format(
                    self._cur_task,
                    epoch + 1,
                    self.args['tuned_epoch'],
                    losses / len(train_loader),
                    disentangle_losses / len(train_loader),
                    train_acc,
                    test_acc,
                )
            else:
                info = "Task {}, Epoch {}/{} => Loss {:.3f}, Train_accy {:.2f}, Test_accy {:.2f}".format(
                    self._cur_task,
                    epoch + 1,
                    self.args['tuned_epoch'],
                    losses / len(train_loader),
                    train_acc,
                    test_acc,
                )
            prog_bar.set_description(info)

        logging.info(info)
