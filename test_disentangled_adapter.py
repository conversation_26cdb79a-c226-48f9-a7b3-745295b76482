"""
解耦适配器测试脚本
验证解耦适配器的基本功能和集成正确性
"""

import torch
import torch.nn as nn
import numpy as np
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backbone.vision_transformer_adapter import DisentangledAdapter
from utils.disentanglement_losses import DisentanglementLossManager
from utils.disentangled_covariance import DisentangledCovarianceCalibrator

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def test_disentangled_adapter():
    """测试解耦适配器的基本功能"""
    print("=" * 50)
    print("测试解耦适配器基本功能")
    print("=" * 50)
    
    try:
        # 创建模拟配置
        from easydict import EasyDict
        config = EasyDict(
            d_model=768,
            attn_bn=64
        )
        
        # 初始化解耦适配器
        adapter = DisentangledAdapter(
            config=config,
            identity_bottleneck=64,
            variation_bottleneck=64,
            disentangle_loss_weight=0.1
        )
        
        # 测试前向传播
        batch_size, seq_len, d_model = 4, 197, 768  # ViT-B/16的典型输入
        x = torch.randn(batch_size, seq_len, d_model)
        
        # 普通前向传播
        output = adapter(x)
        print(f"✓ 普通前向传播成功: 输入 {x.shape} -> 输出 {output.shape}")
        
        # 解耦特征前向传播
        output, identity_features, variation_features = adapter(x, return_separate=True)
        print(f"✓ 解耦前向传播成功:")
        print(f"  - 输出: {output.shape}")
        print(f"  - 身份特征: {identity_features.shape}")
        print(f"  - 变化特征: {variation_features.shape}")
        
        # 测试解耦损失计算
        disentangle_loss = adapter.compute_disentanglement_loss(identity_features, variation_features)
        print(f"✓ 解耦损失计算成功: {disentangle_loss.item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 解耦适配器测试失败: {e}")
        return False


def test_disentanglement_loss_manager():
    """测试解耦损失管理器"""
    print("=" * 50)
    print("测试解耦损失管理器")
    print("=" * 50)
    
    try:
        # 初始化损失管理器
        loss_manager = DisentanglementLossManager(
            decorrelation_weight=1.0,
            orthogonal_weight=0.1,
            mutual_info_weight=0.5,
            contrastive_weight=0.3
        )
        
        # 创建模拟特征
        batch_size = 8
        identity_dim = 32
        variation_dim = 32
        
        identity_features = torch.randn(batch_size, identity_dim)
        variation_features = torch.randn(batch_size, variation_dim)
        labels = torch.randint(0, 4, (batch_size,))
        
        # 测试损失计算
        total_loss, loss_dict = loss_manager(identity_features, variation_features, labels)
        
        print(f"✓ 损失计算成功:")
        print(f"  - 总损失: {total_loss.item():.6f}")
        for loss_name, loss_value in loss_dict.items():
            print(f"  - {loss_name}: {loss_value:.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 解耦损失管理器测试失败: {e}")
        return False


def test_disentangled_covariance_calibrator():
    """测试解耦协方差校准器"""
    print("=" * 50)
    print("测试解耦协方差校准器")
    print("=" * 50)
    
    try:
        # 初始化协方差校准器
        calibrator = DisentangledCovarianceCalibrator(
            variation_cov_weight=0.8,
            identity_similarity_weight=1.0
        )
        
        # 创建模拟数据
        n_samples = 20
        identity_dim = 32
        variation_dim = 32
        original_dim = 64
        
        identity_features = torch.randn(n_samples, identity_dim)
        variation_features = torch.randn(n_samples, variation_dim)
        original_features = torch.randn(n_samples, original_dim)
        
        # 测试协方差计算
        cov_matrix = calibrator.compute_disentangled_covariance(
            identity_features, variation_features, original_features
        )
        print(f"✓ 协方差计算成功: {cov_matrix.shape}")
        
        # 测试相似度权重计算
        n_new, n_base = 3, 5
        new_identity_protos = torch.randn(n_new, identity_dim)
        base_identity_protos = torch.randn(n_base, identity_dim)
        
        similarity_weights = calibrator.compute_identity_similarity_weights(
            new_identity_protos, base_identity_protos, top_k=3
        )
        print(f"✓ 相似度权重计算成功: {similarity_weights.shape}")
        print(f"  - 权重和: {similarity_weights.sum(dim=1)}")
        
        # 测试协方差校准
        new_variation_features_list = [torch.randn(15, variation_dim) for _ in range(n_new)]
        base_covariance_matrices = [torch.eye(original_dim) for _ in range(n_base)]
        original_features_list = [torch.randn(15, original_dim) for _ in range(n_new)]
        
        calibrated_covariances = calibrator.calibrate_covariance_matrices(
            new_variation_features_list,
            new_identity_protos,
            base_covariance_matrices,
            base_identity_protos,
            original_features_list
        )
        print(f"✓ 协方差校准成功: 校准了 {len(calibrated_covariances)} 个协方差矩阵")
        
        return True
        
    except Exception as e:
        print(f"✗ 解耦协方差校准器测试失败: {e}")
        return False


def test_integration():
    """测试集成功能"""
    print("=" * 50)
    print("测试集成功能")
    print("=" * 50)
    
    try:
        # 测试配置文件加载
        import json
        
        # 测试解耦配置
        with open('exps/ranpac_disentangled.json', 'r') as f:
            disentangled_config = json.load(f)
        print(f"✓ 解耦配置文件加载成功")
        print(f"  - use_disentangled_adapter: {disentangled_config.get('use_disentangled_adapter')}")
        
        # 测试向后兼容配置
        with open('exps/ranpac_backward_compatible.json', 'r') as f:
            compatible_config = json.load(f)
        print(f"✓ 向后兼容配置文件加载成功")
        print(f"  - use_disentangled_adapter: {compatible_config.get('use_disentangled_adapter')}")
        
        # 验证配置参数
        required_disentangled_params = [
            'identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight',
            'variation_cov_weight', 'identity_similarity_metric'
        ]
        
        missing_params = []
        for param in required_disentangled_params:
            if param not in disentangled_config:
                missing_params.append(param)
        
        if missing_params:
            print(f"⚠ 缺少参数: {missing_params}")
        else:
            print(f"✓ 所有必要参数都已配置")
        
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始运行解耦适配器测试套件...")
    print()
    
    tests = [
        ("解耦适配器基本功能", test_disentangled_adapter),
        ("解耦损失管理器", test_disentanglement_loss_manager),
        ("解耦协方差校准器", test_disentangled_covariance_calibrator),
        ("集成功能", test_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"✗ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！解耦适配器已准备就绪。")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
