"""
最终验证脚本 - 确保解耦适配器实现的完整性和正确性
"""

import sys
import os
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_file_existence():
    """检查所有必要文件是否存在"""
    print("=" * 50)
    print("检查文件完整性")
    print("=" * 50)
    
    required_files = [
        # 核心实现文件
        "backbone/vision_transformer_adapter.py",
        "utils/disentanglement_losses.py", 
        "utils/disentangled_covariance.py",
        "models/ranpac.py",
        "utils/inc_net.py",
        
        # 配置文件
        "exps/ranpac_disentangled.json",
        "exps/ranpac_backward_compatible.json",
        
        # 测试文件
        "test_disentangled_adapter.py",
        "minimal_test.py",
        "test_ranpac_init.py",
        
        # 文档文件
        "DISENTANGLED_ADAPTER_GUIDE.md",
        "IMPLEMENTATION_SUMMARY.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - 缺失")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠ 缺失 {len(missing_files)} 个文件")
        return False
    else:
        print(f"\n✓ 所有 {len(required_files)} 个文件都存在")
        return True

def check_configuration_validity():
    """检查配置文件的有效性"""
    print("\n" + "=" * 50)
    print("检查配置文件有效性")
    print("=" * 50)
    
    try:
        # 检查解耦配置
        with open('exps/ranpac_disentangled.json', 'r') as f:
            disentangled_config = json.load(f)
        
        required_params = [
            'use_disentangled_adapter', 'identity_bottleneck', 'variation_bottleneck',
            'disentangle_loss_weight', 'variation_cov_weight', 'decorrelation_weight'
        ]
        
        missing_params = []
        for param in required_params:
            if param not in disentangled_config:
                missing_params.append(param)
        
        if missing_params:
            print(f"✗ 解耦配置缺少参数: {missing_params}")
            return False
        else:
            print("✓ 解耦配置参数完整")
        
        # 检查向后兼容配置
        with open('exps/ranpac_backward_compatible.json', 'r') as f:
            compatible_config = json.load(f)
        
        if compatible_config.get('use_disentangled_adapter') == False:
            print("✓ 向后兼容配置正确")
        else:
            print("✗ 向后兼容配置错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件检查失败: {e}")
        return False

def check_import_functionality():
    """检查核心模块的导入功能"""
    print("\n" + "=" * 50)
    print("检查模块导入功能")
    print("=" * 50)
    
    try:
        # 测试解耦适配器导入
        from backbone.vision_transformer_adapter import DisentangledAdapter
        print("✓ DisentangledAdapter 导入成功")
        
        # 测试损失管理器导入
        from utils.disentanglement_losses import DisentanglementLossManager, AdaptiveDisentanglementLoss
        print("✓ 解耦损失管理器导入成功")
        
        # 测试协方差校准器导入
        from utils.disentangled_covariance import DisentangledCovarianceCalibrator
        print("✓ 协方差校准器导入成功")
        
        # 测试RANPAC模型导入
        from models.ranpac import Learner
        print("✓ RANPAC学习器导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_basic_functionality():
    """检查基本功能"""
    print("\n" + "=" * 50)
    print("检查基本功能")
    print("=" * 50)
    
    try:
        import torch
        from backbone.vision_transformer_adapter import DisentangledAdapter
        
        # 创建简单配置
        class SimpleConfig:
            def __init__(self):
                self.d_model = 768
                self.attn_bn = 64
        
        config = SimpleConfig()
        
        # 测试解耦适配器创建
        adapter = DisentangledAdapter(
            config=config,
            identity_bottleneck=32,
            variation_bottleneck=32,
            dropout=0.1
        )
        print("✓ 解耦适配器创建成功")
        
        # 测试前向传播
        x = torch.randn(2, 10, 768)
        output = adapter(x)
        print(f"✓ 前向传播成功: {x.shape} -> {output.shape}")
        
        # 测试解耦特征提取
        output, identity, variation = adapter(x, return_separate=True)
        print(f"✓ 解耦特征提取成功: identity {identity.shape}, variation {variation.shape}")
        
        # 测试解耦损失计算
        loss = adapter.compute_disentanglement_loss(identity, variation)
        print(f"✓ 解耦损失计算成功: {loss.item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_integration_readiness():
    """检查集成就绪状态"""
    print("\n" + "=" * 50)
    print("检查集成就绪状态")
    print("=" * 50)
    
    try:
        # 检查RANPAC参数配置
        test_args = {
            "model_name": "ranpac",
            "backbone_type": "pretrained_vit_b16_224_adapter",
            "device": ["cpu"],
            "batch_size": 32,
            "init_lr": 0.01,
            "ffn_num": 64,
            "nb_classes": 200,
            "use_disentangled_adapter": True,
            "identity_bottleneck": 32,
            "variation_bottleneck": 32,
            "disentangle_loss_weight": 0.1,
        }
        
        # 测试配置验证
        from models.ranpac import Learner
        
        # 注意：这里我们不实际创建学习器，因为可能需要预训练模型
        # 只检查类是否可以导入和基本参数处理
        print("✓ RANPAC学习器类可以导入")
        print("✓ 测试参数配置完整")
        
        # 检查网络配置更新
        from utils.inc_net import get_backbone
        print("✓ 网络配置模块可以导入")
        
        return True
        
    except Exception as e:
        print(f"✗ 集成就绪检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_usage_summary():
    """生成使用总结"""
    print("\n" + "=" * 50)
    print("使用总结")
    print("=" * 50)
    
    print("🎯 解耦泛化适配器已成功实现并集成到RANPAC框架中！")
    print()
    print("📁 核心文件:")
    print("  - backbone/vision_transformer_adapter.py: 双分支适配器实现")
    print("  - utils/disentanglement_losses.py: 解耦损失管理")
    print("  - utils/disentangled_covariance.py: 协方差校准")
    print("  - models/ranpac.py: RANPAC集成")
    print()
    print("⚙️ 配置文件:")
    print("  - exps/ranpac_disentangled.json: 启用解耦适配器")
    print("  - exps/ranpac_backward_compatible.json: 向后兼容模式")
    print()
    print("🧪 测试文件:")
    print("  - test_disentangled_adapter.py: 完整测试套件")
    print("  - minimal_test.py: 快速功能验证")
    print("  - test_ranpac_init.py: RANPAC初始化测试")
    print()
    print("📖 文档:")
    print("  - DISENTANGLED_ADAPTER_GUIDE.md: 详细使用指南")
    print("  - IMPLEMENTATION_SUMMARY.md: 实现总结")
    print()
    print("🚀 快速开始:")
    print("  1. 启用解耦适配器: python main.py --config exps/ranpac_disentangled.json")
    print("  2. 向后兼容模式: python main.py --config exps/ranpac_backward_compatible.json")
    print("  3. 运行测试: python minimal_test.py")

def main():
    """主验证函数"""
    print("🔍 解耦泛化适配器最终验证")
    print("=" * 50)
    
    checks = [
        ("文件完整性", check_file_existence),
        ("配置有效性", check_configuration_validity),
        ("模块导入", check_import_functionality),
        ("基本功能", check_basic_functionality),
        ("集成就绪", check_integration_readiness),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"✗ {check_name} 检查异常: {e}")
            results.append((check_name, False))
    
    # 总结验证结果
    print("\n" + "=" * 50)
    print("验证结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 检查通过")
    
    if passed == total:
        print("\n🎉 所有验证都通过！解耦泛化适配器已准备就绪。")
        generate_usage_summary()
        return True
    else:
        print(f"\n⚠ {total - passed} 项检查失败，请修复相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
