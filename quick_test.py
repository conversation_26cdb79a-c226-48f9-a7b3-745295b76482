"""
快速测试脚本 - 验证解耦适配器的基本导入和初始化
"""

import sys
import os
import torch
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_imports():
    """测试基本导入"""
    try:
        print("测试基本导入...")
        
        # 测试解耦适配器导入
        from backbone.vision_transformer_adapter import DisentangledAdapter
        print("✓ DisentangledAdapter 导入成功")
        
        # 测试损失管理器导入
        from utils.disentanglement_losses import DisentanglementLossManager
        print("✓ DisentanglementLossManager 导入成功")
        
        # 测试协方差校准器导入
        from utils.disentangled_covariance import DisentangledCovarianceCalibrator
        print("✓ DisentangledCovarianceCalibrator 导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\n测试基本功能...")
        
        # 创建模拟配置
        from easydict import EasyDict
        config = EasyDict(
            d_model=768,
            attn_bn=64
        )
        
        # 测试解耦适配器初始化
        from backbone.vision_transformer_adapter import DisentangledAdapter
        adapter = DisentangledAdapter(
            config=config,
            identity_bottleneck=64,
            variation_bottleneck=64,
            dropout=0.1,
            disentangle_loss_weight=0.1
        )
        print("✓ DisentangledAdapter 初始化成功")
        
        # 测试前向传播
        batch_size, seq_len, d_model = 2, 197, 768
        x = torch.randn(batch_size, seq_len, d_model)
        
        # 普通前向传播
        output = adapter(x)
        print(f"✓ 普通前向传播成功: {output.shape}")
        
        # 解耦前向传播
        output, identity_features, variation_features = adapter(x, return_separate=True)
        print(f"✓ 解耦前向传播成功:")
        print(f"  - 输出: {output.shape}")
        print(f"  - 身份特征: {identity_features.shape}")
        print(f"  - 变化特征: {variation_features.shape}")
        
        return True
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置文件加载"""
    try:
        print("\n测试配置文件加载...")
        
        import json
        
        # 测试解耦配置
        with open('exps/ranpac_disentangled.json', 'r') as f:
            disentangled_config = json.load(f)
        print(f"✓ 解耦配置加载成功: use_disentangled_adapter = {disentangled_config.get('use_disentangled_adapter')}")
        
        # 测试向后兼容配置
        with open('exps/ranpac_backward_compatible.json', 'r') as f:
            compatible_config = json.load(f)
        print(f"✓ 向后兼容配置加载成功: use_disentangled_adapter = {compatible_config.get('use_disentangled_adapter')}")
        
        return True
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("解耦适配器快速测试")
    print("=" * 50)
    
    tests = [
        ("基本导入", test_imports),
        ("基本功能", test_basic_functionality),
        ("配置文件加载", test_config_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 快速测试全部通过！")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
