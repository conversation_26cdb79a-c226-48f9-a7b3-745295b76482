#!/usr/bin/env python3
"""
测试损失打印功能
验证各项解耦损失是否能正确显示
"""

import json
import logging
import sys
import os

# 设置日志级别为INFO以显示详细信息
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(filename)s:%(lineno)d] => %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('test_loss_printing.log')
    ]
)

def test_loss_printing():
    """测试损失打印功能"""
    print("=" * 60)
    print("测试解耦损失打印功能")
    print("=" * 60)
    
    try:
        # 加载解耦配置
        with open('exps/ranpac_disentangled.json', 'r') as f:
            config = json.load(f)
        
        print(f"✓ 加载配置文件成功")
        print(f"  - use_disentangled_adapter: {config.get('use_disentangled_adapter', False)}")
        print(f"  - disentangle_loss_weight: {config.get('disentangle_loss_weight', 0.001)}")
        print(f"  - tuned_epoch: {config.get('tuned_epoch', 40)}")
        
        # 检查损失监控器
        from utils.loss_monitor import DisentanglementLossMonitor
        
        monitor = DisentanglementLossMonitor(window_size=10, save_dir="./test_loss_logs")
        print(f"✓ 损失监控器初始化成功")
        
        # 模拟损失数据
        import torch
        import numpy as np
        
        print(f"\n模拟训练过程中的损失打印...")
        
        for epoch in range(3):
            print(f"\n--- Epoch {epoch} ---")
            
            for batch in range(5):
                # 模拟损失值
                loss_dict = {
                    'decorrelation': np.random.uniform(0.001, 0.01),
                    'orthogonal': np.random.uniform(0.0005, 0.005),
                    'mutual_info': np.random.uniform(0.002, 0.02),
                    'contrastive': np.random.uniform(0.001, 0.008),
                    'total': np.random.uniform(0.01, 0.05)
                }
                
                classification_loss = np.random.uniform(0.5, 2.0)
                total_loss = classification_loss + 0.001 * loss_dict['total']
                
                # 更新监控器
                monitor.update_batch_loss(
                    epoch=epoch,
                    batch_idx=batch,
                    loss_dict=loss_dict,
                    classification_loss=classification_loss,
                    total_loss=total_loss
                )
                
                # 模拟详细损失打印（类似训练时的输出）
                if batch % 2 == 0:  # 每2个batch打印一次
                    loss_details = []
                    for loss_name, loss_value in loss_dict.items():
                        loss_details.append(f"{loss_name}={loss_value:.6f}")
                    
                    loss_details_str = ", ".join(loss_details)
                    
                    logging.info(f'[Disentangled Loss Details] Epoch {epoch}, Batch {batch}: '
                               f'cls_loss={classification_loss:.4f}, '
                               f'total_disentangle={loss_dict["total"]:.6f}, '
                               f'weighted_disentangle={0.001 * loss_dict["total"]:.6f}')
                    logging.info(f'[Loss Components] {loss_details_str}')
                    
                    # 计算各项损失占比
                    if loss_dict['total'] > 0:
                        decorr_ratio = loss_dict['decorrelation'] / loss_dict['total'] * 100
                        ortho_ratio = loss_dict['orthogonal'] / loss_dict['total'] * 100
                        mi_ratio = loss_dict['mutual_info'] / loss_dict['total'] * 100
                        contr_ratio = loss_dict['contrastive'] / loss_dict['total'] * 100
                        
                        logging.info(f'[Loss Ratios] decorr={decorr_ratio:.1f}%, ortho={ortho_ratio:.1f}%, '
                                   f'mi={mi_ratio:.1f}%, contr={contr_ratio:.1f}%')
            
            # 模拟epoch结束
            train_acc = np.random.uniform(70, 90)
            test_acc = np.random.uniform(65, 85)
            
            monitor.end_epoch(epoch, train_acc, test_acc)
            
            # 模拟epoch汇总信息
            avg_total_loss = np.random.uniform(1.0, 2.5)
            avg_disentangle_loss = np.random.uniform(0.01, 0.05)
            weighted_disentangle = 0.001 * avg_disentangle_loss
            disentangle_ratio = (weighted_disentangle / avg_total_loss * 100) if avg_total_loss > 0 else 0
            
            logging.info(f"Task 0, Epoch {epoch+1}/3 => Loss {avg_total_loss:.3f}, "
                       f"Disentangle_Loss {avg_disentangle_loss:.3f}, "
                       f"Train_accy {train_acc:.2f}, Test_accy {test_acc:.2f}")
            logging.info(f"[Epoch Summary] Disentangle weight: 0.0010, "
                       f"Weighted disentangle loss: {weighted_disentangle:.6f}, "
                       f"Disentangle ratio: {disentangle_ratio:.1f}% of total loss")
        
        print(f"\n✓ 损失打印测试完成")
        
        # 测试移动平均
        moving_averages = monitor.get_moving_averages()
        print(f"\n移动平均损失:")
        for loss_name, avg_value in moving_averages.items():
            print(f"  {loss_name}: {avg_value:.6f}")
        
        # 保存报告
        report_file = monitor.save_full_report()
        print(f"\n✓ 完整报告已保存到: {report_file}")
        
        # 测试绘图功能
        try:
            monitor.plot_loss_trends(save_path="./test_loss_trends.png")
            print(f"✓ 损失趋势图已保存到: ./test_loss_trends.png")
        except Exception as e:
            print(f"⚠️  绘图功能测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_training():
    """测试实际训练中的损失打印"""
    print(f"\n" + "=" * 60)
    print("测试实际训练配置")
    print("=" * 60)
    
    try:
        # 检查解耦损失管理器
        from utils.disentanglement_losses import DisentanglementLossManager, AdaptiveDisentanglementLoss
        
        # 创建解耦损失管理器
        loss_manager = DisentanglementLossManager(
            decorrelation_weight=0.1,
            orthogonal_weight=0.01,
            mutual_info_weight=0.05,
            contrastive_weight=0.03,
            temperature=0.1
        )
        
        print(f"✓ 解耦损失管理器创建成功")
        
        # 模拟特征数据
        import torch
        batch_size = 32
        identity_dim = 64
        variation_dim = 64
        
        identity_features = torch.randn(batch_size, identity_dim, requires_grad=True)
        variation_features = torch.randn(batch_size, variation_dim, requires_grad=True)
        labels = torch.randint(0, 10, (batch_size,))
        
        # 计算损失
        total_loss, loss_dict = loss_manager(identity_features, variation_features, labels)
        
        print(f"✓ 损失计算成功")
        print(f"  总损失: {total_loss.item():.6f}")
        print(f"  损失详情: {loss_dict}")
        
        # 验证梯度
        total_loss.backward()
        print(f"✓ 梯度计算成功")
        
        # 测试自适应损失管理器
        adaptive_loss_manager = AdaptiveDisentanglementLoss(
            decorrelation_weight=0.1,
            orthogonal_weight=0.01,
            mutual_info_weight=0.05,
            contrastive_weight=0.03,
            temperature=0.1,
            warmup_epochs=10,
            total_epochs=40
        )
        
        print(f"✓ 自适应解耦损失管理器创建成功")
        
        # 测试不同epoch的权重调整
        for epoch in [0, 5, 10, 20, 39]:
            adaptive_loss_manager.update_epoch(epoch)
            adaptive_total_loss, adaptive_loss_dict = adaptive_loss_manager(
                identity_features, variation_features, labels
            )
            print(f"  Epoch {epoch}: 总损失={adaptive_total_loss.item():.6f}, "
                  f"权重调整因子={getattr(adaptive_loss_manager, 'current_weight_factor', 1.0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 实际训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("解耦损失打印功能测试")
    print("=" * 60)
    
    # 测试损失打印
    success1 = test_loss_printing()
    
    # 测试实际训练配置
    success2 = test_actual_training()
    
    # 总结
    print(f"\n" + "=" * 60)
    if success1 and success2:
        print("✅ 所有测试通过！")
        print("现在您可以运行训练脚本，查看详细的损失信息输出")
        print("建议运行命令:")
        print("python main.py --config exps/ranpac_disentangled.json")
    else:
        print("❌ 部分测试失败！")
        print("请检查错误信息并修复相关问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
