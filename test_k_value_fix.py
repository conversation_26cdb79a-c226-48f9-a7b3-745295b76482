#!/usr/bin/env python3
"""
测试K值计算修复效果
验证修复后的动态K值计算是否能产生多样化的K值
"""

import json
import torch
import numpy as np
import sys
import os

def test_k_value_diversity():
    """测试K值多样性"""
    print("=" * 60)
    print("测试K值计算修复效果")
    print("=" * 60)
    
    try:
        # 导入RANPAC学习器
        from models.ranpac import Learner
        
        # 测试修复后的解耦配置
        print("\n1. 测试修复后的解耦配置...")
        
        # 加载解耦配置
        with open('exps/ranpac_disentangled.json', 'r') as f:
            disentangled_config = json.load(f)
        
        # 创建学习器
        learner = Learner(disentangled_config)
        
        # 检查配置
        print(f"✓ use_disentangled_adapter: {learner.use_disentangled_adapter}")
        print(f"✓ use_identity_features_for_knn: {getattr(learner, 'use_identity_features_for_knn', 'Not set')}")
        print(f"✓ knn_distance_metric: {learner.knn_distance_metric}")
        print(f"✓ dynamic_k_method: {learner.dynamic_k_method}")
        
        # 模拟基类相似度统计
        print("\n2. 测试基类相似度统计计算...")
        
        # 创建模拟的基类原型
        init_cls = 100
        feat_dim = 768
        base_protos = torch.randn(init_cls, feat_dim)
        
        # 计算基类相似度统计
        learner._compute_base_similarity_stats(base_protos)
        
        print(f"✓ sim_min: {learner.base_similarity_stats['sim_min']:.4f}")
        print(f"✓ sim_max: {learner.base_similarity_stats['sim_max']:.4f}")
        print(f"✓ range: {learner.base_similarity_stats['sim_max'] - learner.base_similarity_stats['sim_min']:.4f}")
        
        # 测试动态K值计算
        print("\n3. 测试动态K值计算...")
        
        # 创建模拟的新类原型
        n_new = 10
        cur_proto_list = torch.randn(n_new, feat_dim)
        
        # 计算动态K值
        k_values = learner._compute_dynamic_k_values(cur_proto_list, base_protos)
        
        print(f"✓ 计算的K值: {k_values}")
        print(f"✓ K值范围: {min(k_values)} - {max(k_values)}")
        print(f"✓ K值多样性: {len(set(k_values))} 种不同值")
        print(f"✓ 平均K值: {np.mean(k_values):.2f}")
        
        # 评估修复效果
        unique_k_count = len(set(k_values))
        k_range = max(k_values) - min(k_values)
        
        if unique_k_count >= 5 and k_range >= 5:
            print("\n🎉 修复成功！K值显示良好的多样性")
            return True
        elif unique_k_count >= 3 and k_range >= 3:
            print("\n⚠️  修复部分成功，K值有一定多样性但仍需改进")
            return True
        else:
            print("\n❌ 修复失败，K值仍然缺乏多样性")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_configurations():
    """对比不同配置的K值计算结果"""
    print("\n" + "=" * 60)
    print("对比不同配置的K值计算结果")
    print("=" * 60)
    
    configs = {
        "解耦配置": "exps/ranpac_disentangled.json",
        "向后兼容配置": "exps/ranpac_backward_compatible.json",
        "马哈拉诺比斯配置": "exps/ranpac_mahalanobis.json"
    }
    
    results = {}
    
    for config_name, config_path in configs.items():
        try:
            print(f"\n测试 {config_name}...")
            
            # 加载配置
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # 创建学习器
            from models.ranpac import Learner
            learner = Learner(config)
            
            # 模拟数据
            init_cls = 100
            feat_dim = 768
            n_new = 10
            
            base_protos = torch.randn(init_cls, feat_dim)
            cur_proto_list = torch.randn(n_new, feat_dim)
            
            # 计算基类统计
            learner._compute_base_similarity_stats(base_protos)
            
            # 计算K值
            if config.get("knn_distance_metric") == "mahalanobis":
                # 马哈拉诺比斯配置使用不同的方法
                sim_matrix = learner._compute_similarity_matrix(cur_proto_list, base_protos)
                k_values = learner._adaptive_k_selection(sim_matrix, learner.knn_k)
            else:
                k_values = learner._compute_dynamic_k_values(cur_proto_list, base_protos)
            
            results[config_name] = {
                'k_values': k_values,
                'unique_count': len(set(k_values)),
                'k_range': max(k_values) - min(k_values),
                'mean_k': np.mean(k_values),
                'sim_range': learner.base_similarity_stats['sim_max'] - learner.base_similarity_stats['sim_min']
            }
            
            print(f"  K值: {k_values}")
            print(f"  多样性: {results[config_name]['unique_count']} 种不同值")
            print(f"  范围: {results[config_name]['k_range']}")
            
        except Exception as e:
            print(f"  ❌ {config_name} 测试失败: {e}")
            results[config_name] = None
    
    # 总结对比结果
    print(f"\n{'配置':<15} {'K值多样性':<10} {'K值范围':<10} {'平均K值':<10} {'相似度范围':<12}")
    print("-" * 70)
    
    for config_name, result in results.items():
        if result:
            print(f"{config_name:<15} {result['unique_count']:<10} {result['k_range']:<10} {result['mean_k']:<10.2f} {result['sim_range']:<12.4f}")
        else:
            print(f"{config_name:<15} {'失败':<10} {'失败':<10} {'失败':<10} {'失败':<12}")

def main():
    """主函数"""
    print("K值计算修复效果测试")
    print("=" * 60)
    
    # 测试K值多样性
    success = test_k_value_diversity()
    
    # 对比不同配置
    compare_configurations()
    
    # 总结
    print("\n" + "=" * 60)
    if success:
        print("✅ 修复验证通过！")
        print("建议：可以使用修复后的配置进行训练")
    else:
        print("❌ 修复验证失败！")
        print("建议：需要进一步调整修复方案")
    print("=" * 60)

if __name__ == "__main__":
    main()
