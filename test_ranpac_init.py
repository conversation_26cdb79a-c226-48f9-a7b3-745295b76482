"""
测试RANPAC模型初始化
"""

import torch
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_ranpac_with_disentangled():
    """测试启用解耦适配器的RANPAC初始化"""
    print("测试启用解耦适配器的RANPAC初始化...")
    
    try:
        # 创建测试参数
        args = {
            "model_name": "ranpac",
            "backbone_type": "pretrained_vit_b16_224_adapter",
            "device": ["cuda:0" if torch.cuda.is_available() else "cpu"],
            "batch_size": 32,
            "init_lr": 0.01,
            "weight_decay": 0.0005,
            "min_lr": 1e-8,
            "ffn_num": 64,
            "nb_classes": 200,
            "init_cls": 100,
            "increment": 10,
            "tuned_epoch": 40,
            "use_RP": True,
            "M": 1000,
            "calibration": True,
            "knn_k": 5,
            "knn_distance_metric": "cosine",
            
            # 解耦适配器参数
            "use_disentangled_adapter": True,
            "identity_bottleneck": 32,
            "variation_bottleneck": 32,
            "disentangle_loss_weight": 0.1,
            "variation_cov_weight": 0.8,
            "decorrelation_weight": 1.0,
            "orthogonal_weight": 0.1,
            "mutual_info_weight": 0.5,
            "contrastive_weight": 0.3,
        }
        
        # 导入RANPAC学习器
        from models.ranpac import Learner
        
        # 创建学习器
        learner = Learner(args)
        print("✓ RANPAC学习器创建成功")
        
        # 检查解耦适配器是否正确初始化
        if learner.use_disentangled_adapter:
            print("✓ 解耦适配器已启用")
            if learner.disentangle_loss_manager is not None:
                print("✓ 解耦损失管理器已初始化")
            if learner.covariance_calibrator is not None:
                print("✓ 协方差校准器已初始化")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ranpac_backward_compatible():
    """测试向后兼容的RANPAC初始化"""
    print("\n测试向后兼容的RANPAC初始化...")
    
    try:
        # 创建测试参数（不启用解耦适配器）
        args = {
            "model_name": "ranpac",
            "backbone_type": "pretrained_vit_b16_224_adapter",
            "device": ["cuda:0" if torch.cuda.is_available() else "cpu"],
            "batch_size": 32,
            "init_lr": 0.01,
            "weight_decay": 0.0005,
            "min_lr": 1e-8,
            "ffn_num": 64,
            "nb_classes": 200,
            "init_cls": 100,
            "increment": 10,
            "tuned_epoch": 40,
            "use_RP": True,
            "M": 1000,
            "calibration": True,
            "knn_k": 5,
            "knn_distance_metric": "cosine",
            
            # 禁用解耦适配器
            "use_disentangled_adapter": False,
        }
        
        # 导入RANPAC学习器
        from models.ranpac import Learner
        
        # 创建学习器
        learner = Learner(args)
        print("✓ RANPAC学习器创建成功")
        
        # 检查解耦适配器是否正确禁用
        if not learner.use_disentangled_adapter:
            print("✓ 解耦适配器已禁用（向后兼容模式）")
            if learner.disentangle_loss_manager is None:
                print("✓ 解耦损失管理器未初始化")
            if learner.covariance_calibrator is None:
                print("✓ 协方差校准器未初始化")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("RANPAC模型初始化测试")
    print("=" * 50)
    
    tests = [
        ("RANPAC + 解耦适配器", test_ranpac_with_disentangled),
        ("RANPAC 向后兼容", test_ranpac_backward_compatible),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 RANPAC初始化测试全部通过！")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
