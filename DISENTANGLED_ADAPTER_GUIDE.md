# 解耦泛化适配器使用指南

## 概述

解耦泛化适配器是在RANPAC方法基础上的创新扩展，通过双分支架构分离类别身份和类内变化，实现更精确的增量学习。

## 核心创新

### 1. 双分支适配器架构
- **身份分支 (Identity Branch)**: 捕获稳定的类别语义特征，用于分类和相似度计算
- **变化分支 (Variation Branch)**: 捕获类内变化信息，用于协方差建模

### 2. 解耦约束机制
- **去相关损失**: 最小化身份和变化特征之间的相关性
- **正交约束**: 促进特征内部的正交性
- **互信息损失**: 减少身份和变化特征的互信息
- **对比损失**: 确保同类样本的身份特征相似，变化特征不同

### 3. 新的协方差校准范式
- 使用身份特征计算类间相似度权重
- 基于变化特征建模协方差矩阵
- 动态校准新类的协方差矩阵

## 快速开始

### 1. 启用解耦适配器

使用预配置的解耦适配器配置：

```bash
python main.py --config exps/ranpac_disentangled.json
```

### 2. 向后兼容模式

如果需要使用原始RANPAC功能：

```bash
python main.py --config exps/ranpac_backward_compatible.json
```

### 3. 运行测试

验证解耦适配器功能：

```bash
python test_disentangled_adapter.py
```

## 配置参数

### 解耦适配器参数

```json
{
    "use_disentangled_adapter": true,
    "identity_bottleneck": 64,
    "variation_bottleneck": 64
}
```

### 解耦损失参数

```json
{
    "disentangle_loss_weight": 0.1,
    "use_adaptive_disentangle_loss": true,
    "decorrelation_weight": 1.0,
    "orthogonal_weight": 0.1,
    "mutual_info_weight": 0.5,
    "contrastive_weight": 0.3,
    "disentangle_temperature": 0.1,
    "disentangle_warmup_epochs": 10,
    "orthogonal_constraint": true
}
```

### 协方差校准参数

```json
{
    "variation_cov_weight": 0.8,
    "identity_similarity_weight": 1.0,
    "cov_regularization_strength": 1e-6,
    "min_samples_for_cov": 5,
    "identity_similarity_metric": "cosine",
    "identity_similarity_temperature": 16.0,
    "identity_similarity_top_k": 5
}
```

## 参数说明

### 核心参数

- `use_disentangled_adapter`: 是否启用解耦适配器
- `identity_bottleneck`: 身份分支的瓶颈维度
- `variation_bottleneck`: 变化分支的瓶颈维度
- `disentangle_loss_weight`: 解耦损失的权重

### 损失权重

- `decorrelation_weight`: 去相关损失权重
- `orthogonal_weight`: 正交约束权重
- `mutual_info_weight`: 互信息损失权重
- `contrastive_weight`: 对比损失权重

### 协方差校准

- `variation_cov_weight`: 变化特征协方差的权重
- `identity_similarity_metric`: 身份相似度度量方法 (cosine/euclidean/dot_product)
- `identity_similarity_top_k`: 选择最相似的K个基类

## 使用建议

### 1. 参数调优

- **初学者**: 使用默认配置 `ranpac_disentangled.json`
- **进阶用户**: 根据数据集特性调整瓶颈维度和损失权重
- **专家用户**: 自定义相似度度量和校准策略

### 2. 性能优化

- 较小数据集: 减少瓶颈维度 (32-64)
- 较大数据集: 增加瓶颈维度 (64-128)
- 调整 `disentangle_loss_weight` 平衡分类和解耦性能

### 3. 调试技巧

- 启用详细日志: 设置 `logging.DEBUG`
- 监控解耦损失: 观察各项损失的变化趋势
- 验证特征质量: 使用测试脚本检查特征分离效果

## 实验结果

### 预期改进

1. **更好的类间分离**: 身份特征提供更清晰的类别边界
2. **更准确的协方差建模**: 变化特征捕获真实的类内变化
3. **更稳定的增量学习**: 解耦约束减少灾难性遗忘

### 性能指标

- **分类准确率**: 预期提升 2-5%
- **遗忘率**: 预期降低 10-20%
- **新类学习**: 预期提升 3-8%

## 故障排除

### 常见问题

1. **内存不足**: 减少瓶颈维度或批次大小
2. **训练不稳定**: 降低解耦损失权重
3. **性能下降**: 检查配置参数是否合理

### 错误处理

- 自动回退到原始RANPAC实现
- 详细的错误日志和警告信息
- 配置验证和参数检查

## 扩展开发

### 添加新的解耦约束

1. 在 `utils/disentanglement_losses.py` 中添加新的损失函数
2. 在 `DisentanglementLossManager` 中集成新损失
3. 更新配置文件和文档

### 自定义协方差校准

1. 继承 `DisentangledCovarianceCalibrator` 类
2. 重写相关方法实现自定义逻辑
3. 在 `models/ranpac.py` 中替换校准器

## 引用

如果您在研究中使用了解耦泛化适配器，请引用：

```
@article{disentangled_adapter_2024,
  title={Disentangled Generalization Adapter for Few-Shot Class-Incremental Learning},
  author={Your Name},
  journal={arXiv preprint},
  year={2024}
}
```

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- Email: <EMAIL>

---

**注意**: 这是一个实验性功能，建议在正式使用前进行充分测试。
