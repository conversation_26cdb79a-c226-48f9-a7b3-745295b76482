#!/usr/bin/env python3
"""
解耦损失监控工具
实时监控和分析各项解耦损失的变化趋势
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict, deque
import logging
import json
import os
from datetime import datetime


class DisentanglementLossMonitor:
    """解耦损失监控器"""
    
    def __init__(self, window_size=100, save_dir="./loss_logs"):
        """
        初始化损失监控器
        
        Args:
            window_size: 滑动窗口大小，用于计算移动平均
            save_dir: 损失日志保存目录
        """
        self.window_size = window_size
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 损失历史记录
        self.loss_history = defaultdict(deque)
        self.epoch_summaries = []
        
        # 当前epoch的损失累积
        self.current_epoch_losses = defaultdict(list)
        self.current_epoch = 0
        
        # 统计信息
        self.stats = {
            'total_batches': 0,
            'epochs_completed': 0,
            'start_time': datetime.now()
        }
        
        logging.info(f"[Loss Monitor] Initialized with window_size={window_size}, save_dir={save_dir}")
    
    def update_batch_loss(self, epoch, batch_idx, loss_dict, classification_loss=None, total_loss=None):
        """
        更新批次损失信息
        
        Args:
            epoch: 当前epoch
            batch_idx: 批次索引
            loss_dict: 解耦损失字典
            classification_loss: 分类损失
            total_loss: 总损失
        """
        self.current_epoch = epoch
        self.stats['total_batches'] += 1
        
        # 记录各项损失
        for loss_name, loss_value in loss_dict.items():
            if isinstance(loss_value, (int, float)):
                self.loss_history[loss_name].append(loss_value)
                self.current_epoch_losses[loss_name].append(loss_value)
                
                # 维护滑动窗口
                if len(self.loss_history[loss_name]) > self.window_size:
                    self.loss_history[loss_name].popleft()
        
        # 记录其他损失
        if classification_loss is not None:
            self.loss_history['classification'].append(classification_loss)
            self.current_epoch_losses['classification'].append(classification_loss)
            if len(self.loss_history['classification']) > self.window_size:
                self.loss_history['classification'].popleft()
        
        if total_loss is not None:
            self.loss_history['total'].append(total_loss)
            self.current_epoch_losses['total'].append(total_loss)
            if len(self.loss_history['total']) > self.window_size:
                self.loss_history['total'].popleft()
    
    def end_epoch(self, epoch, train_acc=None, test_acc=None):
        """
        结束当前epoch，计算汇总统计
        
        Args:
            epoch: epoch编号
            train_acc: 训练准确率
            test_acc: 测试准确率
        """
        if not self.current_epoch_losses:
            return
        
        # 计算epoch平均损失
        epoch_summary = {
            'epoch': epoch,
            'timestamp': datetime.now().isoformat(),
            'train_acc': train_acc,
            'test_acc': test_acc,
            'losses': {}
        }
        
        for loss_name, loss_values in self.current_epoch_losses.items():
            if loss_values:
                epoch_summary['losses'][loss_name] = {
                    'mean': np.mean(loss_values),
                    'std': np.std(loss_values),
                    'min': np.min(loss_values),
                    'max': np.max(loss_values),
                    'count': len(loss_values)
                }
        
        self.epoch_summaries.append(epoch_summary)
        self.stats['epochs_completed'] += 1
        
        # 打印epoch汇总
        self._print_epoch_summary(epoch_summary)
        
        # 清空当前epoch的累积
        self.current_epoch_losses.clear()
        
        # 保存到文件
        self._save_epoch_summary(epoch_summary)
    
    def _print_epoch_summary(self, summary):
        """打印epoch汇总信息"""
        epoch = summary['epoch']
        losses = summary['losses']
        
        print(f"\n{'='*60}")
        print(f"Epoch {epoch} 解耦损失汇总")
        print(f"{'='*60}")
        
        if 'total' in losses:
            total_loss = losses['total']
            print(f"总损失: {total_loss['mean']:.6f} ± {total_loss['std']:.6f}")
        
        if 'classification' in losses:
            cls_loss = losses['classification']
            print(f"分类损失: {cls_loss['mean']:.6f} ± {cls_loss['std']:.6f}")
        
        print(f"\n解耦损失组件:")
        print(f"{'组件':<15} {'平均值':<12} {'标准差':<12} {'最小值':<12} {'最大值':<12}")
        print(f"{'-'*75}")
        
        disentangle_components = ['decorrelation', 'orthogonal', 'mutual_info', 'contrastive']
        for component in disentangle_components:
            if component in losses:
                loss_info = losses[component]
                print(f"{component:<15} {loss_info['mean']:<12.6f} {loss_info['std']:<12.6f} "
                      f"{loss_info['min']:<12.6f} {loss_info['max']:<12.6f}")
        
        # 计算损失占比
        if 'total' in losses and losses['total']['mean'] > 0:
            print(f"\n解耦损失占比:")
            total_disentangle = sum(losses.get(comp, {}).get('mean', 0) for comp in disentangle_components)
            if total_disentangle > 0:
                for component in disentangle_components:
                    if component in losses:
                        ratio = losses[component]['mean'] / total_disentangle * 100
                        print(f"  {component}: {ratio:.1f}%")
        
        if summary.get('train_acc') is not None:
            print(f"\n训练准确率: {summary['train_acc']:.2f}%")
        if summary.get('test_acc') is not None:
            print(f"测试准确率: {summary['test_acc']:.2f}%")
        
        print(f"{'='*60}\n")
    
    def _save_epoch_summary(self, summary):
        """保存epoch汇总到文件"""
        filename = os.path.join(self.save_dir, f"epoch_{summary['epoch']:03d}_summary.json")
        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)
    
    def get_moving_averages(self, loss_names=None):
        """
        获取指定损失的移动平均值
        
        Args:
            loss_names: 损失名称列表，None表示所有损失
            
        Returns:
            dict: 损失名称到移动平均值的映射
        """
        if loss_names is None:
            loss_names = list(self.loss_history.keys())
        
        moving_averages = {}
        for loss_name in loss_names:
            if loss_name in self.loss_history and self.loss_history[loss_name]:
                moving_averages[loss_name] = np.mean(list(self.loss_history[loss_name]))
        
        return moving_averages
    
    def plot_loss_trends(self, save_path=None):
        """
        绘制损失变化趋势图
        
        Args:
            save_path: 图片保存路径，None表示显示图片
        """
        if not self.epoch_summaries:
            logging.warning("No epoch summaries available for plotting")
            return
        
        # 提取数据
        epochs = [s['epoch'] for s in self.epoch_summaries]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('解耦损失变化趋势', fontsize=16)
        
        # 总损失和分类损失
        ax1 = axes[0, 0]
        total_losses = [s['losses'].get('total', {}).get('mean', 0) for s in self.epoch_summaries]
        cls_losses = [s['losses'].get('classification', {}).get('mean', 0) for s in self.epoch_summaries]
        
        ax1.plot(epochs, total_losses, 'b-', label='总损失', linewidth=2)
        ax1.plot(epochs, cls_losses, 'r-', label='分类损失', linewidth=2)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.set_title('总损失和分类损失')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 解耦损失组件
        ax2 = axes[0, 1]
        components = ['decorrelation', 'orthogonal', 'mutual_info', 'contrastive']
        colors = ['g-', 'm-', 'c-', 'y-']
        
        for component, color in zip(components, colors):
            component_losses = [s['losses'].get(component, {}).get('mean', 0) for s in self.epoch_summaries]
            ax2.plot(epochs, component_losses, color, label=component, linewidth=2)
        
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.set_title('解耦损失组件')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 准确率
        ax3 = axes[1, 0]
        train_accs = [s.get('train_acc', 0) for s in self.epoch_summaries if s.get('train_acc') is not None]
        test_accs = [s.get('test_acc', 0) for s in self.epoch_summaries if s.get('test_acc') is not None]
        
        if train_accs:
            ax3.plot(epochs[:len(train_accs)], train_accs, 'b-', label='训练准确率', linewidth=2)
        if test_accs:
            ax3.plot(epochs[:len(test_accs)], test_accs, 'r-', label='测试准确率', linewidth=2)
        
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Accuracy (%)')
        ax3.set_title('准确率变化')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 损失比例饼图（最后一个epoch）
        ax4 = axes[1, 1]
        if self.epoch_summaries:
            last_epoch = self.epoch_summaries[-1]
            component_values = []
            component_labels = []
            
            for component in components:
                if component in last_epoch['losses']:
                    component_values.append(last_epoch['losses'][component]['mean'])
                    component_labels.append(component)
            
            if component_values:
                ax4.pie(component_values, labels=component_labels, autopct='%1.1f%%')
                ax4.set_title(f'Epoch {last_epoch["epoch"]} 解耦损失组成')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logging.info(f"Loss trend plot saved to {save_path}")
        else:
            plt.show()
    
    def save_full_report(self, filename=None):
        """保存完整的损失报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(self.save_dir, f"loss_report_{timestamp}.json")
        
        report = {
            'stats': self.stats,
            'epoch_summaries': self.epoch_summaries,
            'final_moving_averages': self.get_moving_averages()
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logging.info(f"Full loss report saved to {filename}")
        return filename


# 全局监控器实例
_global_monitor = None

def get_loss_monitor():
    """获取全局损失监控器实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = DisentanglementLossMonitor()
    return _global_monitor

def init_loss_monitor(window_size=100, save_dir="./loss_logs"):
    """初始化全局损失监控器"""
    global _global_monitor
    _global_monitor = DisentanglementLossMonitor(window_size, save_dir)
    return _global_monitor
