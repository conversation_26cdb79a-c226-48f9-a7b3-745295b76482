#!/bin/sh
#SBATCH --partition=gpu-l20
### 指定队列为gpu
#SBATCH --nodes=1
### 指定该作业需要1个节点
#SBATCH --ntasks-per-node=8
### 每个节点上使用42个任务（CPU核心数）
#SBATCH --gres=gpu:1
### 申请1个GPU（与ranpac.json中的设置保持一致）

# 运行FSCIL-Calibration-main-second项目的ranpac训练
CUDA_VISIBLE_DEVICES="0" python /home/<USER>/workdir/FSCIL-Calibration-main/main.py --config /home/<USER>/workdir/FSCIL-Calibration-main/exps/emergency_fix.json

# 如需使用多GPU，请取消注释以下命令并调整exps/ranpac.json中的device设置
# CUDA_VISIBLE_DEVICES="0,1,2" python /home/<USER>/workdir/FSCIL-Calibration-main-second/main.py --config /home/<USER>/workdir/FSCIL-Calibration-main-second/exps/ranpac.json

# 如需使用adam_adapter模型，请取消注释以下命令
# CUDA_VISIBLE_DEVICES="0" python /home/<USER>/workdir/FSCIL-Calibration-main-second/main.py --config /home/<USER>/workdir/FSCIL-Calibration-main-second/exps/adam_adapter.json