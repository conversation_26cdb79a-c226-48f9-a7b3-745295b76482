"""
性能问题诊断脚本
用于诊断解耦适配器导致的性能下降问题
"""

import torch
import numpy as np
import logging
import json
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_config_differences():
    """检查配置文件差异"""
    print("=" * 50)
    print("检查配置文件差异")
    print("=" * 50)
    
    try:
        # 读取原始配置
        with open('exps/ranpac.json', 'r') as f:
            original_config = json.load(f)
        
        # 读取解耦配置
        with open('exps/ranpac_disentangled.json', 'r') as f:
            disentangled_config = json.load(f)
        
        # 读取紧急修复配置
        with open('exps/emergency_fix.json', 'r') as f:
            emergency_config = json.load(f)
        
        print("✓ 配置文件读取成功")
        
        # 比较关键参数
        key_params = ['use_disentangled_adapter', 'M', 'knn_k', 'ffn_num', 'batch_size', 'init_lr']
        
        print("\n关键参数对比:")
        print(f"{'参数':<25} {'原始':<15} {'解耦':<15} {'紧急修复':<15}")
        print("-" * 70)
        
        for param in key_params:
            orig_val = original_config.get(param, 'N/A')
            disentangled_val = disentangled_config.get(param, 'N/A')
            emergency_val = emergency_config.get(param, 'N/A')
            print(f"{param:<25} {str(orig_val):<15} {str(disentangled_val):<15} {str(emergency_val):<15}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件检查失败: {e}")
        return False

def analyze_feature_extraction_path():
    """分析特征提取路径"""
    print("\n" + "=" * 50)
    print("分析特征提取路径")
    print("=" * 50)
    
    try:
        # 模拟RANPAC初始化
        test_args = {
            "model_name": "ranpac",
            "backbone_type": "pretrained_vit_b16_224_adapter",
            "device": ["cpu"],
            "batch_size": 32,
            "init_lr": 0.01,
            "ffn_num": 64,
            "nb_classes": 200,
            "use_disentangled_adapter": False,  # 先测试禁用状态
        }
        
        print("✓ 测试参数创建成功")
        
        # 检查关键路径
        print("\n关键代码路径分析:")
        print("1. replace_fc() 方法中的特征提取")
        print("   - 原始路径: model.extract_vector(data)")
        print("   - 解耦路径: model.backbone.extract_disentangled_features(data)")
        print("   - 当前状态: 已禁用解耦路径，使用原始路径")
        
        print("\n2. 协方差计算路径")
        print("   - 原始方法: torch.cov(embedding.T)")
        print("   - 解耦方法: 基于变化特征的协方差校准")
        print("   - 当前状态: 已禁用解耦协方差校准")
        
        print("\n3. KNN权重计算")
        print("   - 原始方法: _compute_knn_weights()")
        print("   - 解耦方法: _compute_knn_weights_disentangled()")
        print("   - 当前状态: 已禁用解耦KNN权重计算")
        
        return True
        
    except Exception as e:
        print(f"✗ 特征提取路径分析失败: {e}")
        return False

def check_potential_issues():
    """检查潜在问题"""
    print("\n" + "=" * 50)
    print("潜在问题分析")
    print("=" * 50)
    
    issues = [
        {
            "问题": "解耦适配器改变了特征维度",
            "症状": "Ridge参数从10000降到10",
            "原因": "特征分布发生重大变化，导致正则化参数选择异常",
            "修复": "已禁用解耦特征提取，恢复原始特征"
        },
        {
            "问题": "解耦损失干扰了训练",
            "症状": "训练过程中性能下降",
            "原因": "解耦损失权重过大或损失计算有误",
            "修复": "已禁用训练时的解耦损失计算"
        },
        {
            "问题": "协方差校准算法错误",
            "症状": "新类准确率为0%",
            "原因": "解耦协方差校准破坏了原型分类",
            "修复": "已禁用解耦协方差校准"
        },
        {
            "问题": "特征提取异常",
            "症状": "整体性能急剧下降",
            "原因": "解耦适配器的前向传播有问题",
            "修复": "已回退到原始特征提取方法"
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"\n{i}. {issue['问题']}")
        print(f"   症状: {issue['症状']}")
        print(f"   原因: {issue['原因']}")
        print(f"   修复: {issue['修复']}")
    
    return True

def generate_recovery_plan():
    """生成恢复计划"""
    print("\n" + "=" * 50)
    print("性能恢复计划")
    print("=" * 50)
    
    steps = [
        {
            "步骤": "1. 立即恢复",
            "操作": "使用紧急修复配置",
            "命令": "python main.py --config exps/emergency_fix.json",
            "预期": "恢复到原始RANPAC性能（85%左右）"
        },
        {
            "步骤": "2. 验证恢复",
            "操作": "检查性能指标",
            "命令": "观察日志中的准确率和Ridge参数",
            "预期": "Ridge参数恢复到10000，新类准确率>0%"
        },
        {
            "步骤": "3. 逐步调试",
            "操作": "单独启用解耦组件",
            "命令": "修改代码中的 'and False' 为 'and True'",
            "预期": "找出具体问题组件"
        },
        {
            "步骤": "4. 参数调优",
            "操作": "调整解耦损失权重",
            "命令": "减小disentangle_loss_weight到0.01或更小",
            "预期": "平衡分类性能和解耦效果"
        }
    ]
    
    for step in steps:
        print(f"\n{step['步骤']}: {step['操作']}")
        print(f"   命令: {step['命令']}")
        print(f"   预期: {step['预期']}")
    
    return True

def main():
    """主诊断函数"""
    print("🔍 RANPAC性能问题诊断")
    print("=" * 50)
    print("问题描述: 添加解耦适配器后性能从85%下降到0.55%")
    print("=" * 50)
    
    diagnostics = [
        ("配置文件差异", check_config_differences),
        ("特征提取路径", analyze_feature_extraction_path),
        ("潜在问题分析", check_potential_issues),
        ("恢复计划", generate_recovery_plan),
    ]
    
    results = []
    for diag_name, diag_func in diagnostics:
        try:
            result = diag_func()
            results.append((diag_name, result))
        except Exception as e:
            print(f"✗ {diag_name} 诊断异常: {e}")
            results.append((diag_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("诊断总结")
    print("=" * 50)
    
    print("🚨 紧急修复已应用:")
    print("   - 禁用解耦特征提取")
    print("   - 禁用解耦协方差校准")
    print("   - 禁用解耦损失计算")
    print("   - 禁用解耦KNN权重计算")
    
    print("\n🎯 立即行动:")
    print("   1. 运行: python main.py --config exps/emergency_fix.json")
    print("   2. 检查性能是否恢复到85%左右")
    print("   3. 如果恢复成功，再逐步启用解耦组件进行调试")
    
    print("\n📊 预期结果:")
    print("   - 总体准确率: 85% (从0.55%恢复)")
    print("   - 新类准确率: >0% (从0%恢复)")
    print("   - Ridge参数: 10000 (从10恢复)")
    print("   - HM值: >0 (从0恢复)")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
