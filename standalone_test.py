"""
独立测试脚本 - 不依赖外部库的解耦适配器核心功能测试
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

# 简化的解耦适配器实现（用于测试）
class SimplifiedDisentangledAdapter(nn.Module):
    """简化的解耦适配器，用于独立测试"""
    
    def __init__(self, 
                 d_model=768,
                 identity_bottleneck=64,
                 variation_bottleneck=64,
                 dropout=0.1,
                 disentangle_loss_weight=0.1):
        super().__init__()
        
        self.d_model = d_model
        self.identity_bottleneck = identity_bottleneck
        self.variation_bottleneck = variation_bottleneck
        self.dropout = dropout
        self.disentangle_loss_weight = disentangle_loss_weight
        
        # 身份分支
        self.identity_branch = nn.Sequential(
            nn.Linear(d_model, identity_bottleneck),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 变化分支
        self.variation_branch = nn.Sequential(
            nn.Linear(d_model, variation_bottleneck),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 输出投影
        self.identity_output_dim = d_model // 2
        self.variation_output_dim = d_model // 2
        
        self.identity_output_proj = nn.Linear(identity_bottleneck, self.identity_output_dim)
        self.variation_output_proj = nn.Linear(variation_bottleneck, self.variation_output_dim)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.kaiming_uniform_(module.weight, a=math.sqrt(5))
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x, return_separate=False):
        """前向传播"""
        # 双分支处理
        identity_features = self.identity_branch(x)
        variation_features = self.variation_branch(x)
        
        # 输出投影
        identity_output = self.identity_output_proj(identity_features)
        variation_output = self.variation_output_proj(variation_features)
        
        # 拼接特征
        combined_output = torch.cat([identity_output, variation_output], dim=-1)
        
        if return_separate:
            return combined_output, identity_output, variation_output
        else:
            return combined_output
    
    def compute_disentanglement_loss(self, identity_features, variation_features):
        """计算解耦损失"""
        # 简化的去相关损失
        identity_centered = identity_features - identity_features.mean(dim=0, keepdim=True)
        variation_centered = variation_features - variation_features.mean(dim=0, keepdim=True)
        
        # 归一化
        identity_norm = F.normalize(identity_centered, p=2, dim=0)
        variation_norm = F.normalize(variation_centered, p=2, dim=0)
        
        # 计算互相关矩阵
        cross_correlation = torch.mm(identity_norm.T, variation_norm)
        
        # 去相关损失
        decorrelation_loss = torch.norm(cross_correlation, p='fro') ** 2
        
        return decorrelation_loss * self.disentangle_loss_weight

def test_simplified_adapter():
    """测试简化的解耦适配器"""
    print("=" * 50)
    print("测试简化解耦适配器")
    print("=" * 50)
    
    try:
        # 创建适配器
        adapter = SimplifiedDisentangledAdapter(
            d_model=768,
            identity_bottleneck=64,
            variation_bottleneck=64,
            dropout=0.1
        )
        print("✓ 简化解耦适配器创建成功")
        
        # 测试前向传播
        batch_size, seq_len, d_model = 4, 197, 768
        x = torch.randn(batch_size, seq_len, d_model)
        
        # 普通前向传播
        output = adapter(x)
        print(f"✓ 前向传播成功: {x.shape} -> {output.shape}")
        
        # 解耦前向传播
        output, identity_features, variation_features = adapter(x, return_separate=True)
        print(f"✓ 解耦前向传播成功:")
        print(f"  - 输出: {output.shape}")
        print(f"  - 身份特征: {identity_features.shape}")
        print(f"  - 变化特征: {variation_features.shape}")
        
        # 测试解耦损失
        disentangle_loss = adapter.compute_disentanglement_loss(identity_features, variation_features)
        print(f"✓ 解耦损失计算成功: {disentangle_loss.item():.6f}")
        
        # 测试梯度计算
        loss = output.sum() + disentangle_loss
        loss.backward()
        print("✓ 梯度计算成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_concepts():
    """测试核心概念"""
    print("\n" + "=" * 50)
    print("测试核心概念")
    print("=" * 50)
    
    try:
        # 测试特征解耦
        batch_size = 8
        identity_dim = 32
        variation_dim = 32
        
        # 模拟身份特征（同类相似）
        identity_features = torch.randn(batch_size, identity_dim)
        
        # 模拟变化特征（类内变化）
        variation_features = torch.randn(batch_size, variation_dim)
        
        print(f"✓ 特征生成成功: identity {identity_features.shape}, variation {variation_features.shape}")
        
        # 测试相似度计算
        identity_sim = F.cosine_similarity(identity_features.unsqueeze(1), identity_features.unsqueeze(0), dim=2)
        variation_sim = F.cosine_similarity(variation_features.unsqueeze(1), variation_features.unsqueeze(0), dim=2)
        
        print(f"✓ 相似度计算成功: identity_sim {identity_sim.shape}, variation_sim {variation_sim.shape}")
        
        # 测试协方差计算
        identity_cov = torch.cov(identity_features.T)
        variation_cov = torch.cov(variation_features.T)
        
        print(f"✓ 协方差计算成功: identity_cov {identity_cov.shape}, variation_cov {variation_cov.shape}")
        
        # 测试KNN权重计算
        n_new, n_base = 3, 5
        new_protos = torch.randn(n_new, identity_dim)
        base_protos = torch.randn(n_base, identity_dim)
        
        # 计算相似度矩阵
        new_norm = F.normalize(new_protos, p=2, dim=-1)
        base_norm = F.normalize(base_protos, p=2, dim=-1)
        sim_matrix = torch.mm(new_norm, base_norm.T)
        
        # 计算top-k权重
        k = 3
        topk_vals, topk_indices = torch.topk(sim_matrix, k, dim=1)
        weights = torch.softmax(topk_vals, dim=1)
        
        print(f"✓ KNN权重计算成功: sim_matrix {sim_matrix.shape}, weights {weights.shape}")
        print(f"  权重和: {weights.sum(dim=1)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 核心概念测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n" + "=" * 50)
    print("测试文件结构")
    print("=" * 50)
    
    import os
    import json
    
    try:
        # 检查核心文件
        core_files = [
            "backbone/vision_transformer_adapter.py",
            "utils/disentanglement_losses.py",
            "utils/disentangled_covariance.py",
            "models/ranpac.py"
        ]
        
        for file_path in core_files:
            if os.path.exists(file_path):
                print(f"✓ {file_path}")
            else:
                print(f"✗ {file_path} - 缺失")
                return False
        
        # 检查配置文件
        config_files = [
            "exps/ranpac_disentangled.json",
            "exps/ranpac_backward_compatible.json"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                print(f"✓ {config_file} - 有效JSON")
            else:
                print(f"✗ {config_file} - 缺失")
                return False
        
        # 检查文档文件
        doc_files = [
            "DISENTANGLED_ADAPTER_GUIDE.md",
            "IMPLEMENTATION_SUMMARY.md"
        ]
        
        for doc_file in doc_files:
            if os.path.exists(doc_file):
                print(f"✓ {doc_file}")
            else:
                print(f"✗ {doc_file} - 缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 文件结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 解耦泛化适配器独立测试")
    print("=" * 50)
    print("注意：此测试不依赖外部库（如timm），仅验证核心概念和文件结构")
    print("=" * 50)
    
    tests = [
        ("简化解耦适配器", test_simplified_adapter),
        ("核心概念", test_core_concepts),
        ("文件结构", test_file_structure),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 独立测试全部通过！")
        print("\n📝 下一步:")
        print("1. 安装必要依赖: pip install timm easydict")
        print("2. 运行完整测试: python test_disentangled_adapter.py")
        print("3. 启动训练: python main.py --config exps/ranpac_disentangled.json")
        return True
    else:
        print(f"\n⚠ {total - passed} 项测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
