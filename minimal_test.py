"""
最小化测试 - 只测试解耦适配器的核心功能
"""

import torch
import torch.nn as nn

def test_disentangled_adapter():
    """测试解耦适配器"""
    print("测试解耦适配器...")

    try:
        # 导入
        from backbone.vision_transformer_adapter import DisentangledAdapter

        # 创建简单配置对象
        class SimpleConfig:
            def __init__(self):
                self.d_model = 768
                self.attn_bn = 64

        config = SimpleConfig()

        # 创建适配器
        adapter = DisentangledAdapter(
            config=config,
            identity_bottleneck=32,
            variation_bottleneck=32,
            dropout=0.1
        )
        
        print(f"✓ 适配器创建成功")
        
        # 测试前向传播
        x = torch.randn(2, 10, 768)
        output = adapter(x)
        print(f"✓ 前向传播成功: {x.shape} -> {output.shape}")
        
        # 测试解耦特征
        output, identity, variation = adapter(x, return_separate=True)
        print(f"✓ 解耦特征提取成功:")
        print(f"  身份特征: {identity.shape}")
        print(f"  变化特征: {variation.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_disentangled_adapter()
    if success:
        print("🎉 最小化测试通过！")
    else:
        print("❌ 测试失败")
